<?php
/**
 * IVAC Dashboard Admin Area
 */

if (!defined('ABSPATH')) {
    exit;
}

class IVAC_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('IVAC Dashboard', 'ivac-dashboard'),
            __('IVAC Dashboard', 'ivac-dashboard'),
            'manage_options',
            'ivac-dashboard',
            array($this, 'admin_page'),
            'dashicons-list-view',
            30
        );
        
        add_submenu_page(
            'ivac-dashboard',
            __('Settings', 'ivac-dashboard'),
            __('Settings', 'ivac-dashboard'),
            'manage_options',
            'ivac-dashboard-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'ivac-dashboard',
            __('API Keys', 'ivac-dashboard'),
            __('API Keys', 'ivac-dashboard'),
            'manage_options',
            'ivac-dashboard-api-keys',
            array($this, 'api_keys_page')
        );
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        register_setting('ivac_dashboard_settings', 'ivac_dashboard_settings');
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('IVAC Dashboard', 'ivac-dashboard'); ?></h1>
            <p><?php _e('Use the shortcode [ivac_dashboard] to display the dashboard on any page or post.', 'ivac-dashboard'); ?></p>
            
            <div class="card">
                <h2><?php _e('Quick Stats', 'ivac-dashboard'); ?></h2>
                <?php $this->display_quick_stats(); ?>
            </div>
            
            <div class="card">
                <h2><?php _e('Recent Files', 'ivac-dashboard'); ?></h2>
                <?php $this->display_recent_files(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        if (isset($_POST['submit'])) {
            $settings = array(
                'enable_notifications' => isset($_POST['enable_notifications']),
                'auto_process_jobs' => isset($_POST['auto_process_jobs']),
                'max_concurrent_jobs' => intval($_POST['max_concurrent_jobs'])
            );
            update_option('ivac_dashboard_settings', $settings);
            echo '<div class="notice notice-success"><p>' . __('Settings saved.', 'ivac-dashboard') . '</p></div>';
        }
        
        $settings = get_option('ivac_dashboard_settings', array());
        ?>
        <div class="wrap">
            <h1><?php _e('IVAC Dashboard Settings', 'ivac-dashboard'); ?></h1>
            
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Notifications', 'ivac-dashboard'); ?></th>
                        <td>
                            <input type="checkbox" name="enable_notifications" value="1" <?php checked(isset($settings['enable_notifications']) ? $settings['enable_notifications'] : false); ?> />
                            <p class="description"><?php _e('Enable email notifications for job status changes.', 'ivac-dashboard'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Auto Process Jobs', 'ivac-dashboard'); ?></th>
                        <td>
                            <input type="checkbox" name="auto_process_jobs" value="1" <?php checked(isset($settings['auto_process_jobs']) ? $settings['auto_process_jobs'] : false); ?> />
                            <p class="description"><?php _e('Automatically process pending jobs when Chrome extension is connected.', 'ivac-dashboard'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Max Concurrent Jobs', 'ivac-dashboard'); ?></th>
                        <td>
                            <input type="number" name="max_concurrent_jobs" value="<?php echo isset($settings['max_concurrent_jobs']) ? $settings['max_concurrent_jobs'] : 5; ?>" min="1" max="20" />
                            <p class="description"><?php _e('Maximum number of jobs to process simultaneously.', 'ivac-dashboard'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * API Keys page
     */
    public function api_keys_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('API Keys Management', 'ivac-dashboard'); ?></h1>
            <p><?php _e('Manage API keys for Chrome extension access.', 'ivac-dashboard'); ?></p>
            
            <div id="ivac-api-keys-app"></div>
            
            <script>
                // This would be replaced with a proper React component
                document.addEventListener('DOMContentLoaded', function() {
                    const container = document.getElementById('ivac-api-keys-app');
                    container.innerHTML = `
                        <div class="card">
                            <h2>Generate New API Key</h2>
                            <form id="generate-key-form">
                                <table class="form-table">
                                    <tr>
                                        <th><label for="key-name">Key Name</label></th>
                                        <td><input type="text" id="key-name" name="name" required /></td>
                                    </tr>
                                </table>
                                <button type="submit" class="button button-primary">Generate Key</button>
                            </form>
                        </div>
                        <div class="card">
                            <h2>Existing API Keys</h2>
                            <div id="api-keys-list">Loading...</div>
                        </div>
                    `;
                    
                    // Load existing keys
                    loadApiKeys();
                    
                    // Handle form submission
                    document.getElementById('generate-key-form').addEventListener('submit', function(e) {
                        e.preventDefault();
                        generateApiKey();
                    });
                });
                
                function loadApiKeys() {
                    fetch('<?php echo rest_url('ivac/v1/auth/keys'); ?>', {
                        headers: {
                            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        const container = document.getElementById('api-keys-list');
                        if (data.keys && data.keys.length > 0) {
                            container.innerHTML = data.keys.map(key => `
                                <div class="api-key-item" style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
                                    <strong>${key.name}</strong><br>
                                    <code>${key.api_key_masked}</code><br>
                                    <small>Created: ${key.created_at} | Last used: ${key.last_used || 'Never'}</small><br>
                                    <button onclick="revokeKey(${key.id})" class="button button-secondary">Revoke</button>
                                </div>
                            `).join('');
                        } else {
                            container.innerHTML = '<p>No API keys found.</p>';
                        }
                    });
                }
                
                function generateApiKey() {
                    const name = document.getElementById('key-name').value;
                    
                    fetch('<?php echo rest_url('ivac/v1/auth/generate-key'); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
                        },
                        body: JSON.stringify({ name: name })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('API Key generated successfully! Key: ' + data.api_key);
                            document.getElementById('key-name').value = '';
                            loadApiKeys();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    });
                }
                
                function revokeKey(keyId) {
                    if (confirm('Are you sure you want to revoke this API key?')) {
                        fetch('<?php echo rest_url('ivac/v1/auth/keys/'); ?>' + keyId, {
                            method: 'DELETE',
                            headers: {
                                'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert('API key revoked successfully');
                                loadApiKeys();
                            } else {
                                alert('Error: ' + data.message);
                            }
                        });
                    }
                }
            </script>
        </div>
        <?php
    }
    
    /**
     * Display quick stats
     */
    private function display_quick_stats() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_files';
        
        $stats = $wpdb->get_results(
            "SELECT status, COUNT(*) as count FROM $table_name GROUP BY status",
            ARRAY_A
        );
        
        $total = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        
        echo '<div style="display: flex; gap: 20px;">';
        echo '<div><strong>Total Files:</strong> ' . $total . '</div>';
        
        foreach ($stats as $stat) {
            $status_label = ucfirst(str_replace('_', ' ', $stat['status']));
            echo '<div><strong>' . $status_label . ':</strong> ' . $stat['count'] . '</div>';
        }
        echo '</div>';
    }
    
    /**
     * Display recent files
     */
    private function display_recent_files() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_files';
        
        $recent_files = $wpdb->get_results(
            "SELECT id, name, email, status, created_at FROM $table_name ORDER BY created_at DESC LIMIT 10",
            ARRAY_A
        );
        
        if (empty($recent_files)) {
            echo '<p>' . __('No files found.', 'ivac-dashboard') . '</p>';
            return;
        }
        
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>ID</th><th>Name</th><th>Email</th><th>Status</th><th>Created</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($recent_files as $file) {
            echo '<tr>';
            echo '<td>' . $file['id'] . '</td>';
            echo '<td>' . esc_html($file['name']) . '</td>';
            echo '<td>' . esc_html($file['email']) . '</td>';
            echo '<td><span class="status-' . $file['status'] . '">' . ucfirst($file['status']) . '</span></td>';
            echo '<td>' . date('Y-m-d H:i', strtotime($file['created_at'])) . '</td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
    }
}
