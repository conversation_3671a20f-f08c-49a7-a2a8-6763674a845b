<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IVAC Login</title>
    <style>
        /* ফন্ট ইম্পোর্ট */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

        body {
            font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        /* মূল কন্টেইনার */
        #ivac-login-container {
            /* background: #C71585; */
            padding: 20px;
            border-radius: 12px;
            /* box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); */
            width: 300px;
            border: 2px solid #ccc;
            color: white;
        }

        /* শিরোনাম (এখন আর ড্র্যাগ করা যাবে না) */
        h3 {
            margin: 0 0 15px 0;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            letter-spacing: 1px;
            text-transform: uppercase;
            background: #ccc;
            padding: 10px 0;
            border-radius: 6px;
            color: #000;
        }

        /* স্ট্যাটাস প্যানেল */
        #ivac-status-panel {
            padding: 10px;
            margin: 0 0 10px 0;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 6px;
            font-size: 12px;
            min-height: 20px;
            text-align: center;
            color: #333;
            font-weight: 500;
            word-wrap: break-word;
        }

        /* ইনপুট ফিল্ড */
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        
        input::placeholder {
            color: #777;
        }

        /* বাটন */
        #login-button {
            width: 100%;
            padding: 10px;
            background-color: #000;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #login-button:hover {
            background-color: #000; /* Purple */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        #login-button:disabled {
            background-color: #999;
            cursor: not-allowed;
        }
    </style>
</head>
<body>

    <div id="ivac-login-container">
        <h3>Al Imran Akash</h3>
        <div id="ivac-status-panel">Ready to Login</div>
        <input type="text" id="ivac-userid-input" placeholder="User ID">
        <input type="password" id="ivac-password-input" placeholder="Password">
        <button id="login-button">Login</button>
    </div>

    <script>
        // DOM এলিমেন্টগুলো সিলেক্ট করা
        const statusPanel = document.getElementById('ivac-status-panel');
        const userIdInput = document.getElementById('ivac-userid-input');
        const passwordInput = document.getElementById('ivac-password-input');
        const loginButton = document.getElementById('login-button');

        // কনফিগারেশন
        const config = {
            endpoints: {
                login: 'https://payment.ivacbd.com/dologin',
                admin: 'https://payment.ivacbd.com/iv-admin',
                dashboard: 'https://payment.ivacbd.com/'
            },
            storageKey: 'ivac_credentials',
            retry: {
                maxRetries: 5,
                initialDelay: 1000,
            }
        };
        
        // স্ট্যাটাস আপডেট করার ফাংশন
        function updateStatus(message, isError = false) {
            console.log(`[${isError ? 'ERROR' : 'INFO'}] ${message}`);
            statusPanel.textContent = message;
            statusPanel.style.color = isError ? '#D32F2F' : '#388E3C';
        }

        // লোকাল স্টোরেজ থেকে তথ্য লোড করা
        function loadCredentials() {
            try {
                const savedCreds = localStorage.getItem(config.storageKey);
                if (savedCreds) {
                    const { userId, password } = JSON.parse(savedCreds);
                    if (userId) userIdInput.value = userId;
                    if (password) passwordInput.value = password;
                    updateStatus('Saved credentials loaded.');
                }
            } catch (e) {
                updateStatus('Failed to load credentials.', true);
            }
        }

        // লোকাল স্টোরেজে তথ্য সেভ করা
        function saveCredentials(userId, password) {
            try {
                localStorage.setItem(config.storageKey, JSON.stringify({ userId, password }));
            } catch (e) {
                updateStatus('Failed to save credentials.', true);
            }
        }

        /**
         * CSRF টোকেন খুঁজে বের করার ফাংশন (মূল কোড থেকে নেওয়া)
         * এটি শুধুমাত্র ব্রাউজারের কনসোলে বা এক্সটেনশনের মাধ্যমে কাজ করবে যখন আপনি টার্গেট সাইটে থাকবেন।
         */
        function getCsrfToken() {
            // যদি পেজে window.csrf_token নামে জাভাস্ক্রিপ্ট ভ্যারিয়েবল থাকে
            if (typeof window.csrf_token !== 'undefined') {
                return window.csrf_token;
            }
            
            // মেটা ট্যাগ থেকে খোঁজার চেষ্টা
            const metaTag = document.querySelector('meta[name="csrf-token"]');
            if (metaTag && metaTag.content) {
                return metaTag.content;
            }
            
            // ইনপুট ফিল্ড থেকে খোঁজার চেষ্টা
            const inputField = document.querySelector('input[name="_token"]');
            if (inputField) {
                return inputField.value;
            }

            // স্ক্রিপ্ট ট্যাগ থেকে রেজেক্স দিয়ে খোঁজার চেষ্টা
            const scripts = document.querySelectorAll('script');
            for (let script of scripts) {
                const match = script.innerHTML.match(/var csrf_token = "([^"]+)"/);
                if (match && match[1]) {
                    return match[1];
                }
            }
            
            return null;
        }

        // মূল লগইন ফাংশন
        async function handleLogin() {
            const userId = userIdInput.value.trim();
            const password = passwordInput.value.trim();

            if (!userId || !password) {
                updateStatus('Please enter User ID and Password.', true);
                return;
            }

            loginButton.disabled = true;
            updateStatus('Attempting to log in...');
            saveCredentials(userId, password);

            // CSRF টোকেন পাওয়ার চেষ্টা করা
            // এই অংশটি কাজ করার জন্য এই HTML ফাইলটি payment.ivacbd.com ডোমেইনে চলতে হবে
            // সরাসরি ফাইল খুললে এটি null রিটার্ন করবে।
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                updateStatus('CSRF token not found. Cannot proceed. Run this on the target website.', true);
                loginButton.disabled = false;
                return;
            }
            updateStatus(`CSRF Token found: ${csrfToken.substring(0, 10)}...`);

            const formData = new URLSearchParams();
            formData.append('user_id', userId);
            formData.append('password', password);
            formData.append('_token', csrfToken); // CSRF টোকেন যোগ করা

            try {
                const response = await fetch(config.endpoints.login, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest' // এটি গুরুত্বপূর্ণ হতে পারে
                    },
                    body: formData,
                    redirect: 'manual' // Redirect নিজে হ্যান্ডেল করার জন্য
                });

                // স্ট্যাটাস 302 মানে সফলভাবে রিডাইরেক্ট হয়েছে
                if (response.status === 302 || response.status === 200) {
                    updateStatus('Login successful! Redirecting to dashboard...');
                    // সফল হলে ড্যাশবোর্ডে রিডাইরেক্ট করা
                    setTimeout(() => {
                        window.location.href = config.endpoints.dashboard;
                    }, 1500);
                } else {
                    // যদি অন্য কোনো স্ট্যাটাস কোড আসে
                    let responseText = await response.text();
                    try {
                        const jsonResponse = JSON.parse(responseText);
                        updateStatus(jsonResponse.message || 'Login failed.', true);
                    } catch {
                         updateStatus(`Login failed with status: ${response.status}`, true);
                    }
                    loginButton.disabled = false;
                }
            } catch (error) {
                updateStatus(`Network error: ${error.message}`, true);
                loginButton.disabled = false;
            }
        }

        // ইভেন্ট লিসেনার যোগ করা
        loginButton.addEventListener('click', handleLogin);
        document.addEventListener('DOMContentLoaded', loadCredentials);
    </script>

</body>
</html>