# IVAC Pro Automation Chrome Extension

A comprehensive Chrome browser extension designed to automate the Indian Visa Application Center (IVAC) process on `https://payment.ivacbd.com/`.

## Features

- **Automated Login**: Handles user authentication with mobile number and password
- **Form Auto-Fill**: Automatically fills application forms with pre-configured data
- **reCAPTCHA Solving**: Integrates with third-party captcha solving services
- **OTP Handling**: Manages One-Time Password verification
- **Payment Processing**: Generates payment links for completed applications
- **Real-time Monitoring**: Live status updates and progress tracking
- **Activity Logging**: Comprehensive logging with export functionality
- **Statistics Tracking**: Job processing metrics and success rates

## Architecture

The extension consists of three main components:

1. **Chrome Extension (Client)**: User interface and browser automation
2. **Backend Server API**: Job management and external service integration
3. **Third-party Services**: reCAPTCHA solving and OTP handling

## File Structure

```
IVAC-Pro-Automation/
├── manifest.json          # Extension manifest (Manifest V3)
├── popup.html             # Main control panel interface
├── popup.css              # Popup styling
├── popup.js               # Popup functionality and communication
├── options.html           # Settings configuration page
├── options.css            # Options page styling
├── options.js             # Settings management
├── content.js             # Content script for DOM manipulation
├── background.js          # Service worker for workflow orchestration
├── icons/
│   └── icon48.png         # Extension icon
└── README.md              # This file
```

## Installation

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the `IVAC-Pro-Automation` folder
5. The extension will appear in your Chrome toolbar

## Configuration

1. Click the extension icon and select "Settings"
2. Configure the following sections:

### Backend Settings
- **API Key**: Your backend server API key
- **Server URL**: Backend server endpoint URL

### IVAC Credentials
- **Mobile Number**: Your IVAC account mobile number
- **Password**: Your IVAC account password

### Default Application Info
- **Web File ID**: Default web file identifier
- **Applicant Name**: Full name of the applicant
- **Email**: Contact email address
- **Preferred Date**: Preferred appointment date
- **Visa Type**: Type of visa being applied for
- **Visit Reason**: Purpose of visit
- **IVAC Center**: Preferred IVAC center location

### Automation Settings
- **Retry Attempts**: Number of retry attempts for failed operations
- **Delay Between Actions**: Delay in milliseconds between automated actions
- **Enable Notifications**: Toggle for browser notifications

## Usage

1. Ensure all settings are properly configured
2. Click the extension icon to open the control panel
3. Click "Start Automation" to begin the automated process
4. Monitor progress through the real-time status indicators
5. View detailed logs in the Activity Log section
6. Use quick action buttons (Pause, Skip, Emergency Stop) as needed

## API Endpoints

The extension communicates with the following backend API endpoints:

- `GET /api/jobs/next` - Fetch next pending job
- `POST /api/jobs/update` - Update job status
- `POST /api/captcha/solve` - Solve reCAPTCHA challenges
- `GET /api/otp/get` - Retrieve OTP codes
- `POST /api/auth/validate` - Validate API key

## Security Considerations

- All sensitive data is stored locally using Chrome's storage API
- API communications use HTTPS with bearer token authentication
- Passwords are handled securely and never logged
- The extension only operates on the specified IVAC domain

## Troubleshooting

### Common Issues

1. **Extension not loading**: Ensure all files are present and manifest.json is valid
2. **Settings not saving**: Check Chrome storage permissions
3. **Automation not starting**: Verify all required settings are configured
4. **reCAPTCHA failures**: Check backend server and captcha service status
5. **OTP not received**: Verify phone number and backend OTP service

### Debug Mode

Enable Chrome Developer Tools and check the Console tab for detailed error messages:
1. Right-click the extension popup → "Inspect"
2. Navigate to the Console tab
3. Look for error messages and warnings

## Development

### Prerequisites
- Chrome browser with Developer mode enabled
- Basic knowledge of JavaScript, HTML, and CSS
- Understanding of Chrome Extension APIs

### Testing
1. Load the extension in developer mode
2. Test each component individually
3. Monitor console logs for errors
4. Use Chrome's extension debugging tools

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review console logs for error details
3. Ensure all dependencies are properly configured
4. Verify backend server connectivity

## License

This project is for educational and automation purposes. Please ensure compliance with IVAC terms of service and applicable laws.

## Disclaimer

This extension is designed to automate legitimate visa application processes. Users are responsible for ensuring compliance with all applicable terms of service and regulations. The developers are not responsible for any misuse or violations.
