/**
 * IVAC Pro Automation - Background Script (Service Worker)
 * Orchestrates the entire automation workflow
 */

class IVACBackgroundService {
    constructor() {
        this.isRunning = false;
        this.isPaused = false;
        this.currentJob = null;
        this.currentStep = 'login';
        this.settings = {};
        this.stats = {
            jobsProcessed: 0,
            successCount: 0,
            errorCount: 0
        };
        
        this.init();
    }

    /**
     * Initialize background service
     */
    init() {
        console.log('IVAC Pro Automation: Background service initialized');
        
        // Listen for extension messages
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Listen for tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // Load settings and stats
        this.loadSettings();
        this.loadStats();
    }

    /**
     * Handle messages from popup and content scripts
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            console.log('Background received message:', message);
            
            switch (message.action) {
                case 'startAutomation':
                    const startResult = await this.startAutomation();
                    sendResponse(startResult);
                    break;
                    
                case 'stopAutomation':
                    const stopResult = await this.stopAutomation();
                    sendResponse(stopResult);
                    break;
                    
                case 'pauseAutomation':
                    const pauseResult = await this.pauseAutomation();
                    sendResponse(pauseResult);
                    break;
                    
                case 'skipCurrentJob':
                    const skipResult = await this.skipCurrentJob();
                    sendResponse(skipResult);
                    break;
                    
                case 'emergencyStop':
                    const emergencyResult = await this.emergencyStop();
                    sendResponse(emergencyResult);
                    break;
                    
                case 'getStatus':
                    sendResponse({
                        isRunning: this.isRunning,
                        isPaused: this.isPaused,
                        currentStep: this.currentStep,
                        currentJob: this.currentJob,
                        stats: this.stats
                    });
                    break;
                    
                case 'settingsUpdated':
                    this.settings = message.settings;
                    sendResponse({ success: true });
                    break;
                    
                case 'contentScriptReady':
                    console.log('Content script ready on:', message.url);
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action: ' + message.action });
            }
        } catch (error) {
            console.error('Background script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * Handle tab updates
     */
    handleTabUpdate(tabId, changeInfo, tab) {
        if (changeInfo.status === 'complete' && tab.url && tab.url.includes('payment.ivacbd.com')) {
            console.log('IVAC page loaded:', tab.url);
            
            // Notify popup about page change
            this.notifyPopup('statusUpdate', {
                isRunning: this.isRunning,
                currentStep: this.detectStepFromUrl(tab.url),
                currentJob: this.currentJob
            });
        }
    }

    /**
     * Start automation workflow
     */
    async startAutomation() {
        if (this.isRunning) {
            return { success: false, error: 'Automation is already running' };
        }

        try {
            // Validate settings
            if (!this.settings.apiKey || !this.settings.mobileNumber || !this.settings.password) {
                return { success: false, error: 'Please configure settings first' };
            }

            this.isRunning = true;
            this.isPaused = false;
            this.currentStep = 'login';
            
            this.notifyPopup('logUpdate', { type: 'info', message: 'Starting automation workflow...' });
            this.notifyPopup('statusUpdate', { isRunning: true, currentStep: 'login' });
            
            // Start the main workflow
            this.startAutomationWorkflow();
            
            return { success: true };
        } catch (error) {
            this.isRunning = false;
            return { success: false, error: error.message };
        }
    }

    /**
     * Stop automation
     */
    async stopAutomation() {
        this.isRunning = false;
        this.isPaused = false;
        this.currentJob = null;
        this.currentStep = 'login';
        
        this.notifyPopup('logUpdate', { type: 'info', message: 'Automation stopped' });
        this.notifyPopup('statusUpdate', { isRunning: false, currentStep: 'login' });
        
        return { success: true };
    }

    /**
     * Pause automation
     */
    async pauseAutomation() {
        if (!this.isRunning) {
            return { success: false, error: 'Automation is not running' };
        }
        
        this.isPaused = !this.isPaused;
        const status = this.isPaused ? 'paused' : 'resumed';
        
        this.notifyPopup('logUpdate', { type: 'info', message: `Automation ${status}` });
        
        return { success: true, paused: this.isPaused };
    }

    /**
     * Skip current job
     */
    async skipCurrentJob() {
        if (!this.isRunning || !this.currentJob) {
            return { success: false, error: 'No job to skip' };
        }
        
        this.notifyPopup('logUpdate', { type: 'warning', message: `Skipping job: ${this.currentJob}` });
        
        // Update job status on server
        await this.updateJobStatus(this.currentJob, 'skipped');
        
        // Move to next job
        this.currentJob = null;
        
        return { success: true };
    }

    /**
     * Emergency stop
     */
    async emergencyStop() {
        this.isRunning = false;
        this.isPaused = false;
        this.currentJob = null;
        this.currentStep = 'login';
        
        // Close all IVAC tabs
        const tabs = await chrome.tabs.query({ url: '*://payment.ivacbd.com/*' });
        for (const tab of tabs) {
            chrome.tabs.remove(tab.id);
        }
        
        this.notifyPopup('logUpdate', { type: 'error', message: 'Emergency stop activated - all operations halted' });
        this.notifyPopup('statusUpdate', { isRunning: false, currentStep: 'login' });
        
        return { success: true };
    }

    /**
     * Main automation workflow
     */
    async startAutomationWorkflow() {
        while (this.isRunning) {
            try {
                // Check if paused
                if (this.isPaused) {
                    await this.delay(1000);
                    continue;
                }

                // Step 1: Fetch next job from server
                this.currentStep = 'fetching_job';
                this.notifyPopup('progressUpdate', { step: 'login', progress: 10 });

                const job = await this.fetchNextJob();
                if (!job) {
                    this.notifyPopup('logUpdate', { type: 'info', message: 'No pending jobs. Waiting...' });
                    await this.delay(30000); // Wait 30 seconds before checking again
                    continue;
                }

                this.currentJob = job.jobId;
                this.notifyPopup('jobUpdate', { jobId: job.jobId });
                this.notifyPopup('logUpdate', { type: 'info', message: `Processing job: ${job.jobId}` });

                // Step 2: Navigate to IVAC website
                this.currentStep = 'navigation';
                this.notifyPopup('progressUpdate', { step: 'login', progress: 20 });

                const tab = await this.navigateToIVAC();

                // Step 3: Handle login process
                this.currentStep = 'login';
                this.notifyPopup('progressUpdate', { step: 'login', progress: 30 });

                await this.handleLogin(tab.id, job.applicationData);

                // Step 4: Fill application form
                this.currentStep = 'application';
                this.notifyPopup('progressUpdate', { step: 'application', progress: 60 });

                await this.handleApplicationForm(tab.id, job.applicationData);

                // Step 5: Handle payment process
                this.currentStep = 'payment';
                this.notifyPopup('progressUpdate', { step: 'payment', progress: 80 });

                const paymentLink = await this.handlePayment(tab.id, job.applicationData);

                // Step 6: Complete job
                this.currentStep = 'complete';
                this.notifyPopup('progressUpdate', { step: 'complete', progress: 100 });

                await this.completeJob(job.jobId, paymentLink);

                // Update stats
                this.stats.jobsProcessed++;
                this.stats.successCount++;
                this.saveStats();
                this.notifyPopup('statsUpdate', { stats: this.stats });

                this.notifyPopup('logUpdate', { type: 'success', message: `Job ${job.jobId} completed successfully` });

                // Clean up
                this.currentJob = null;
                this.currentStep = 'login';

                // Small delay before next job
                await this.delay(5000);

            } catch (error) {
                console.error('Workflow error:', error);
                this.notifyPopup('logUpdate', { type: 'error', message: `Workflow error: ${error.message}` });

                // Update error stats
                this.stats.errorCount++;
                this.saveStats();

                // Update job status if we have a current job
                if (this.currentJob) {
                    await this.updateJobStatus(this.currentJob, 'error', error.message);
                    this.currentJob = null;
                }

                // Wait before retrying
                await this.delay(10000);
            }
        }
    }

    /**
     * Fetch next job from backend server
     */
    async fetchNextJob() {
        try {
            const response = await fetch(`${this.settings.serverUrl}/api/jobs/next`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.settings.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.message === 'No pending jobs.') {
                return null;
            }

            return data;
        } catch (error) {
            console.error('Error fetching next job:', error);
            throw new Error(`Failed to fetch job: ${error.message}`);
        }
    }

    /**
     * Navigate to IVAC website
     */
    async navigateToIVAC() {
        const tab = await chrome.tabs.create({
            url: 'https://payment.ivacbd.com/',
            active: false
        });

        // Wait for tab to load
        await this.waitForTabLoad(tab.id);

        return tab;
    }

    /**
     * Handle login process
     */
    async handleLogin(tabId, applicationData) {
        this.notifyPopup('logUpdate', { type: 'info', message: 'Starting login process...' });

        // Wait for page to be ready
        await this.delay(2000);

        // Fill login form
        const loginResult = await chrome.tabs.sendMessage(tabId, {
            action: 'fillLoginForm',
            data: {
                mobileNumber: this.settings.mobileNumber,
                password: this.settings.password
            }
        });

        if (!loginResult.success) {
            throw new Error('Failed to fill login form: ' + loginResult.error);
        }

        // Handle reCAPTCHA if present
        await this.handleRecaptcha(tabId);

        // Handle OTP if required
        await this.handleOTP(tabId, this.settings.mobileNumber);

        this.notifyPopup('logUpdate', { type: 'success', message: 'Login completed successfully' });
    }

    /**
     * Handle application form
     */
    async handleApplicationForm(tabId, applicationData) {
        this.notifyPopup('logUpdate', { type: 'info', message: 'Filling application form...' });

        // Fill application form
        const formResult = await chrome.tabs.sendMessage(tabId, {
            action: 'fillApplicationForm',
            data: applicationData
        });

        if (!formResult.success) {
            throw new Error('Failed to fill application form: ' + formResult.error);
        }

        // Handle any additional reCAPTCHA
        await this.handleRecaptcha(tabId);

        this.notifyPopup('logUpdate', { type: 'success', message: 'Application form completed' });
    }

    /**
     * Handle payment process
     */
    async handlePayment(tabId, applicationData) {
        this.notifyPopup('logUpdate', { type: 'info', message: 'Processing payment...' });

        // Handle payment OTP
        await this.handleOTP(tabId, this.settings.mobileNumber);

        // Select appointment date if needed
        if (applicationData.preferredDate) {
            await chrome.tabs.sendMessage(tabId, {
                action: 'selectDate',
                data: { date: applicationData.preferredDate }
            });
        }

        // Extract payment link
        const pageInfo = await chrome.tabs.sendMessage(tabId, {
            action: 'getPageInfo'
        });

        if (!pageInfo.success) {
            throw new Error('Failed to get page info: ' + pageInfo.error);
        }

        // Look for payment link in page
        const paymentLink = await chrome.tabs.sendMessage(tabId, {
            action: 'extractData',
            data: {
                selectors: {
                    paymentLink: 'a[href*="payment"], a[href*="pay"], .payment-link'
                }
            }
        });

        if (paymentLink.success && paymentLink.data.paymentLink) {
            return paymentLink.data.paymentLink;
        }

        // If no direct link found, return current page URL as fallback
        return pageInfo.data.url;
    }

    /**
     * Handle reCAPTCHA solving
     */
    async handleRecaptcha(tabId) {
        try {
            // Check if reCAPTCHA is present
            const recaptchaDetails = await chrome.tabs.sendMessage(tabId, {
                action: 'getRecaptchaDetails'
            });

            if (!recaptchaDetails.success) {
                // No reCAPTCHA found, continue
                return;
            }

            this.notifyPopup('logUpdate', { type: 'info', message: 'Solving reCAPTCHA...' });

            // Send reCAPTCHA to backend for solving
            const solveResponse = await fetch(`${this.settings.serverUrl}/api/captcha/solve`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.settings.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    siteKey: recaptchaDetails.data.siteKey,
                    pageUrl: recaptchaDetails.data.pageUrl
                })
            });

            if (!solveResponse.ok) {
                throw new Error(`reCAPTCHA solve failed: ${solveResponse.statusText}`);
            }

            const solveResult = await solveResponse.json();

            // Submit the solved token
            const submitResult = await chrome.tabs.sendMessage(tabId, {
                action: 'submitRecaptchaToken',
                data: { token: solveResult.token }
            });

            if (!submitResult.success) {
                throw new Error('Failed to submit reCAPTCHA token: ' + submitResult.error);
            }

            this.notifyPopup('logUpdate', { type: 'success', message: 'reCAPTCHA solved successfully' });

        } catch (error) {
            console.error('reCAPTCHA handling error:', error);
            this.notifyPopup('logUpdate', { type: 'warning', message: 'reCAPTCHA handling failed: ' + error.message });
            // Don't throw error as reCAPTCHA might not be critical
        }
    }

    /**
     * Handle OTP process
     */
    async handleOTP(tabId, phoneNumber) {
        try {
            this.notifyPopup('logUpdate', { type: 'info', message: 'Waiting for OTP...' });

            // Request OTP from backend
            const otpResponse = await fetch(`${this.settings.serverUrl}/api/otp/get?phone=${phoneNumber}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.settings.apiKey}`
                }
            });

            if (!otpResponse.ok) {
                throw new Error(`OTP request failed: ${otpResponse.statusText}`);
            }

            const otpResult = await otpResponse.json();

            // Fill OTP in the form
            const fillResult = await chrome.tabs.sendMessage(tabId, {
                action: 'fillOTP',
                data: { otp: otpResult.otp }
            });

            if (!fillResult.success) {
                throw new Error('Failed to fill OTP: ' + fillResult.error);
            }

            this.notifyPopup('logUpdate', { type: 'success', message: 'OTP filled successfully' });

        } catch (error) {
            console.error('OTP handling error:', error);
            this.notifyPopup('logUpdate', { type: 'warning', message: 'OTP handling failed: ' + error.message });
            // Don't throw error as OTP might not always be required
        }
    }

    /**
     * Complete job and update server
     */
    async completeJob(jobId, paymentLink) {
        try {
            await this.updateJobStatus(jobId, 'completed', null, paymentLink);
            this.notifyPopup('logUpdate', { type: 'success', message: `Job ${jobId} marked as completed` });
        } catch (error) {
            console.error('Error completing job:', error);
            throw new Error(`Failed to complete job: ${error.message}`);
        }
    }

    /**
     * Update job status on server
     */
    async updateJobStatus(jobId, status, errorMessage = null, paymentLink = null) {
        try {
            const updateData = {
                jobId: jobId,
                status: status
            };

            if (errorMessage) {
                updateData.error = errorMessage;
            }

            if (paymentLink) {
                updateData.paymentLink = paymentLink;
            }

            const response = await fetch(`${this.settings.serverUrl}/api/jobs/update`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.settings.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('Job status updated:', result);

        } catch (error) {
            console.error('Error updating job status:', error);
            throw error;
        }
    }

    /**
     * Wait for tab to finish loading
     */
    async waitForTabLoad(tabId, timeout = 30000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkTab = async () => {
                try {
                    const tab = await chrome.tabs.get(tabId);

                    if (tab.status === 'complete') {
                        resolve(tab);
                        return;
                    }

                    if (Date.now() - startTime > timeout) {
                        reject(new Error('Tab load timeout'));
                        return;
                    }

                    setTimeout(checkTab, 500);
                } catch (error) {
                    reject(error);
                }
            };

            checkTab();
        });
    }

    /**
     * Detect current step from URL
     */
    detectStepFromUrl(url) {
        if (url.includes('login')) return 'login';
        if (url.includes('application')) return 'application';
        if (url.includes('payment') || url.includes('pay')) return 'payment';
        if (url.includes('success') || url.includes('complete')) return 'complete';
        return 'unknown';
    }

    /**
     * Notify popup about updates
     */
    notifyPopup(action, data) {
        chrome.runtime.sendMessage({
            action: action,
            ...data
        }).catch(error => {
            // Popup might not be open, ignore error
            console.log('Could not notify popup:', error.message);
        });
    }

    /**
     * Load settings from storage
     */
    async loadSettings() {
        try {
            const stored = await chrome.storage.local.get([
                'apiKey', 'serverUrl', 'mobileNumber', 'password',
                'webFileId', 'applicantName', 'email', 'preferredDate',
                'visaType', 'visitReason', 'ivacCenter', 'retryAttempts',
                'delayBetweenActions', 'enableNotifications'
            ]);

            this.settings = stored;
            console.log('Settings loaded:', Object.keys(this.settings));
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    /**
     * Load stats from storage
     */
    async loadStats() {
        try {
            const stored = await chrome.storage.local.get('stats');
            if (stored.stats) {
                this.stats = { ...this.stats, ...stored.stats };
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    }

    /**
     * Save stats to storage
     */
    async saveStats() {
        try {
            await chrome.storage.local.set({ stats: this.stats });
        } catch (error) {
            console.error('Error saving stats:', error);
        }
    }

    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Show notification
     */
    showNotification(title, message, type = 'basic') {
        if (this.settings.enableNotifications) {
            chrome.notifications.create({
                type: type,
                iconUrl: 'icons/icon48.png',
                title: title,
                message: message
            });
        }
    }
}

// Initialize background service
new IVACBackgroundService();
