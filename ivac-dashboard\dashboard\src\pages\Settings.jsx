import React, { useState, useEffect } from 'react'
import { Save, RefreshCw } from 'lucide-react'
import toast from 'react-hot-toast'

const Settings = () => {
  const [loading, setLoading] = useState(false)
  const [settings, setSettings] = useState({
    enable_notifications: false,
    auto_process_jobs: false,
    max_concurrent_jobs: 5,
    api_timeout: 30,
    retry_attempts: 3,
    captcha_service: 'manual',
    captcha_api_key: '',
    notification_email: '',
    webhook_url: ''
  })

  // Load settings on component mount
  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      // In a real implementation, this would fetch from WordPress options
      const savedSettings = localStorage.getItem('ivac_dashboard_settings')
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings))
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)

    try {
      // In a real implementation, this would save to WordPress options via REST API
      localStorage.setItem('ivac_dashboard_settings', JSON.stringify(settings))
      toast.success('Settings saved successfully!')
    } catch (error) {
      toast.error('Failed to save settings')
    } finally {
      setLoading(false)
    }
  }

  const resetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
      setSettings({
        enable_notifications: false,
        auto_process_jobs: false,
        max_concurrent_jobs: 5,
        api_timeout: 30,
        retry_attempts: 3,
        captcha_service: 'manual',
        captcha_api_key: '',
        notification_email: '',
        webhook_url: ''
      })
      toast.success('Settings reset to defaults')
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
        <p className="text-gray-600">Configure your IVAC Dashboard preferences and automation settings</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* General Settings */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">General Settings</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="form-label">Enable Notifications</label>
                <p className="form-help">Send email notifications for job status changes</p>
              </div>
              <input
                type="checkbox"
                name="enable_notifications"
                checked={settings.enable_notifications}
                onChange={handleInputChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="form-label">Auto Process Jobs</label>
                <p className="form-help">Automatically process pending jobs when Chrome extension is connected</p>
              </div>
              <input
                type="checkbox"
                name="auto_process_jobs"
                checked={settings.auto_process_jobs}
                onChange={handleInputChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Notification Email</label>
              <input
                type="email"
                name="notification_email"
                value={settings.notification_email}
                onChange={handleInputChange}
                className="input"
                placeholder="Enter email for notifications"
              />
              <p className="form-help">Email address to receive status notifications</p>
            </div>
          </div>
        </div>

        {/* Processing Settings */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Processing Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">Max Concurrent Jobs</label>
              <input
                type="number"
                name="max_concurrent_jobs"
                value={settings.max_concurrent_jobs}
                onChange={handleInputChange}
                min="1"
                max="20"
                className="input"
              />
              <p className="form-help">Maximum number of jobs to process simultaneously</p>
            </div>

            <div className="form-group">
              <label className="form-label">API Timeout (seconds)</label>
              <input
                type="number"
                name="api_timeout"
                value={settings.api_timeout}
                onChange={handleInputChange}
                min="10"
                max="300"
                className="input"
              />
              <p className="form-help">Timeout for API requests</p>
            </div>

            <div className="form-group">
              <label className="form-label">Retry Attempts</label>
              <input
                type="number"
                name="retry_attempts"
                value={settings.retry_attempts}
                onChange={handleInputChange}
                min="0"
                max="10"
                className="input"
              />
              <p className="form-help">Number of retry attempts for failed jobs</p>
            </div>
          </div>
        </div>

        {/* CAPTCHA Settings */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">CAPTCHA Settings</h3>
          
          <div className="space-y-4">
            <div className="form-group">
              <label className="form-label">CAPTCHA Service</label>
              <select
                name="captcha_service"
                value={settings.captcha_service}
                onChange={handleInputChange}
                className="select"
              >
                <option value="manual">Manual Solving</option>
                <option value="2captcha">2captcha</option>
                <option value="anticaptcha">Anti-Captcha</option>
                <option value="deathbycaptcha">DeathByCaptcha</option>
              </select>
              <p className="form-help">Choose how to handle CAPTCHA challenges</p>
            </div>

            {settings.captcha_service !== 'manual' && (
              <div className="form-group">
                <label className="form-label">CAPTCHA API Key</label>
                <input
                  type="password"
                  name="captcha_api_key"
                  value={settings.captcha_api_key}
                  onChange={handleInputChange}
                  className="input"
                  placeholder="Enter your CAPTCHA service API key"
                />
                <p className="form-help">API key for the selected CAPTCHA solving service</p>
              </div>
            )}
          </div>
        </div>

        {/* Integration Settings */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Integration Settings</h3>
          
          <div className="form-group">
            <label className="form-label">Webhook URL</label>
            <input
              type="url"
              name="webhook_url"
              value={settings.webhook_url}
              onChange={handleInputChange}
              className="input"
              placeholder="https://your-webhook-url.com/endpoint"
            />
            <p className="form-help">URL to receive webhook notifications for job status changes</p>
          </div>
        </div>

        {/* System Information */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Plugin Version:</span>
              <span className="ml-2 text-gray-600">1.0.0</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">WordPress Version:</span>
              <span className="ml-2 text-gray-600">{window.IVAC_CONFIG?.wpVersion || 'Unknown'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">PHP Version:</span>
              <span className="ml-2 text-gray-600">{window.IVAC_CONFIG?.phpVersion || 'Unknown'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Database Version:</span>
              <span className="ml-2 text-gray-600">{window.IVAC_CONFIG?.dbVersion || 'Unknown'}</span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <button
            type="button"
            onClick={resetSettings}
            className="btn-outline"
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </button>
          
          <button
            type="submit"
            className="btn-primary"
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="loading-spinner h-4 w-4 mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}

export default Settings
