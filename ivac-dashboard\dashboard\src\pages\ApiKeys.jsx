import React, { useState, useEffect } from 'react'
import { Plus, Trash2, <PERSON><PERSON>, <PERSON>, <PERSON>Off, Key, Calendar, Clock } from 'lucide-react'
import toast from 'react-hot-toast'
import { authAPI } from '../services/api'

const ApiKeys = () => {
  const [keys, setKeys] = useState([])
  const [loading, setLoading] = useState(false)
  const [showNewKeyForm, setShowNewKeyForm] = useState(false)
  const [newKeyName, setNewKeyName] = useState('')
  const [generatedKey, setGeneratedKey] = useState(null)
  const [visibleKeys, setVisibleKeys] = useState(new Set())

  useEffect(() => {
    loadApiKeys()
  }, [])

  const loadApiKeys = async () => {
    setLoading(true)
    try {
      const response = await authAPI.getKeys()
      setKeys(response.data.keys || [])
    } catch (error) {
      toast.error('Failed to load API keys')
      console.error('Error loading API keys:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateApiKey = async (e) => {
    e.preventDefault()
    
    if (!newKeyName.trim()) {
      toast.error('Please enter a name for the API key')
      return
    }

    setLoading(true)
    try {
      const response = await authAPI.generateKey(newKeyName.trim())
      
      if (response.data.success) {
        setGeneratedKey({
          name: response.data.name,
          key: response.data.api_key,
          created_at: response.data.created_at
        })
        setNewKeyName('')
        setShowNewKeyForm(false)
        await loadApiKeys()
        toast.success('API key generated successfully!')
      } else {
        toast.error(response.data.message || 'Failed to generate API key')
      }
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to generate API key')
    } finally {
      setLoading(false)
    }
  }

  const revokeApiKey = async (keyId, keyName) => {
    if (!confirm(`Are you sure you want to revoke the API key "${keyName}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await authAPI.revokeKey(keyId)
      
      if (response.data.success) {
        await loadApiKeys()
        toast.success('API key revoked successfully')
      } else {
        toast.error(response.data.message || 'Failed to revoke API key')
      }
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to revoke API key')
    }
  }

  const copyToClipboard = async (text, label) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success(`${label} copied to clipboard!`)
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      toast.success(`${label} copied to clipboard!`)
    }
  }

  const toggleKeyVisibility = (keyId) => {
    const newVisibleKeys = new Set(visibleKeys)
    if (newVisibleKeys.has(keyId)) {
      newVisibleKeys.delete(keyId)
    } else {
      newVisibleKeys.add(keyId)
    }
    setVisibleKeys(newVisibleKeys)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (isActive) => {
    return isActive ? 'badge-success' : 'badge-error'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">API Keys</h2>
          <p className="text-gray-600">Manage API keys for Chrome extension access</p>
        </div>
        <button
          onClick={() => setShowNewKeyForm(true)}
          className="btn-primary"
          disabled={loading}
        >
          <Plus className="h-4 w-4 mr-2" />
          Generate New Key
        </button>
      </div>

      {/* Generated Key Display */}
      {generatedKey && (
        <div className="card bg-green-50 border-green-200">
          <div className="flex items-start space-x-3">
            <Key className="h-5 w-5 text-green-600 mt-0.5" />
            <div className="flex-1">
              <h3 className="font-semibold text-green-900">New API Key Generated</h3>
              <p className="text-green-700 text-sm mb-3">
                Please copy this API key now. You won't be able to see it again.
              </p>
              
              <div className="bg-white border border-green-300 rounded-md p-3 mb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900">{generatedKey.name}</div>
                    <div className="font-mono text-sm text-gray-600 break-all">
                      {generatedKey.key}
                    </div>
                  </div>
                  <button
                    onClick={() => copyToClipboard(generatedKey.key, 'API Key')}
                    className="btn-outline btn-sm ml-3"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </button>
                </div>
              </div>
              
              <button
                onClick={() => setGeneratedKey(null)}
                className="text-green-600 hover:text-green-800 text-sm font-medium"
              >
                I've copied the key, dismiss this message
              </button>
            </div>
          </div>
        </div>
      )}

      {/* New Key Form */}
      {showNewKeyForm && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Generate New API Key</h3>
          
          <form onSubmit={generateApiKey} className="space-y-4">
            <div className="form-group">
              <label className="form-label">Key Name</label>
              <input
                type="text"
                value={newKeyName}
                onChange={(e) => setNewKeyName(e.target.value)}
                className="input"
                placeholder="Enter a descriptive name for this API key"
                required
              />
              <p className="form-help">
                Choose a name that helps you identify where this key will be used
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                type="submit"
                className="btn-primary"
                disabled={loading || !newKeyName.trim()}
              >
                {loading ? (
                  <>
                    <div className="loading-spinner h-4 w-4 mr-2"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <Key className="h-4 w-4 mr-2" />
                    Generate Key
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowNewKeyForm(false)
                  setNewKeyName('')
                }}
                className="btn-outline"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* API Keys List */}
      <div className="card p-0">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Existing API Keys</h3>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="loading-spinner h-8 w-8"></div>
            <span className="ml-2 text-gray-600">Loading API keys...</span>
          </div>
        ) : keys.length === 0 ? (
          <div className="text-center py-12">
            <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-500 mb-4">No API keys found</div>
            <button
              onClick={() => setShowNewKeyForm(true)}
              className="btn-primary"
            >
              <Plus className="h-4 w-4 mr-2" />
              Generate Your First API Key
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {keys.map((key) => (
              <div key={key.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium text-gray-900">{key.name}</h4>
                      <span className={`badge ${getStatusBadge(key.is_active)}`}>
                        {key.is_active ? 'Active' : 'Revoked'}
                      </span>
                    </div>
                    
                    <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Created {formatDate(key.created_at)}</span>
                      </div>
                      {key.last_used && (
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>Last used {formatDate(key.last_used)}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-3 flex items-center space-x-2">
                      <div className="font-mono text-sm bg-gray-100 px-3 py-1 rounded border">
                        {visibleKeys.has(key.id) ? key.api_key_masked : key.api_key_masked}
                      </div>
                      <button
                        onClick={() => toggleKeyVisibility(key.id)}
                        className="p-1 hover:bg-gray-100 rounded"
                        title={visibleKeys.has(key.id) ? 'Hide key' : 'Show key'}
                      >
                        {visibleKeys.has(key.id) ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                      <button
                        onClick={() => copyToClipboard(key.api_key_masked, 'API Key')}
                        className="p-1 hover:bg-gray-100 rounded"
                        title="Copy to clipboard"
                      >
                        <Copy className="h-4 w-4 text-gray-400" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {key.is_active && (
                      <button
                        onClick={() => revokeApiKey(key.id, key.name)}
                        className="btn-outline btn-sm text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        Revoke
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div className="card bg-blue-50 border-blue-200">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Key className="h-4 w-4 text-blue-600" />
            </div>
          </div>
          <div>
            <h3 className="font-semibold text-blue-900">How to use API Keys</h3>
            <div className="text-blue-800 text-sm mt-1 space-y-2">
              <p>1. Copy the generated API key and paste it into your Chrome extension settings</p>
              <p>2. The extension will use this key to authenticate with the dashboard</p>
              <p>3. Keep your API keys secure and don't share them publicly</p>
              <p>4. Revoke keys that are no longer needed or have been compromised</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ApiKeys
