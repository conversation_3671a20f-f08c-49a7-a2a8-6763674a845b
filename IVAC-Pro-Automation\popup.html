<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IVAC Pro Automation</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="popup-container">
        <header class="popup-header">
            <div class="logo">
                <img src="icons/icon48.png" alt="IVAC Pro" class="logo-icon">
                <h1>IVAC Pro</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Ready</span>
            </div>
        </header>

        <main class="popup-main">
            <!-- Control Panel -->
            <section class="control-panel">
                <div class="main-controls">
                    <button id="toggleButton" class="toggle-button start">
                        <span class="button-icon">▶</span>
                        <span class="button-text">Start Automation</span>
                    </button>
                    
                    <button id="settingsButton" class="settings-button">
                        <span class="button-icon">⚙</span>
                        Settings
                    </button>
                </div>

                <!-- Quick Stats -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Jobs Processed</span>
                        <span class="stat-value" id="jobsProcessed">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Success Rate</span>
                        <span class="stat-value" id="successRate">0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Current Job</span>
                        <span class="stat-value" id="currentJob">None</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Runtime</span>
                        <span class="stat-value" id="runtime">00:00:00</span>
                    </div>
                </div>
            </section>

            <!-- Progress Section -->
            <section class="progress-section">
                <h3>Current Progress</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-steps">
                    <div class="step" data-step="login">
                        <div class="step-icon">1</div>
                        <span class="step-label">Login</span>
                    </div>
                    <div class="step" data-step="application">
                        <div class="step-icon">2</div>
                        <span class="step-label">Application</span>
                    </div>
                    <div class="step" data-step="payment">
                        <div class="step-icon">3</div>
                        <span class="step-label">Payment</span>
                    </div>
                    <div class="step" data-step="complete">
                        <div class="step-icon">✓</div>
                        <span class="step-label">Complete</span>
                    </div>
                </div>
            </section>

            <!-- Log Section -->
            <section class="log-section">
                <div class="log-header">
                    <h3>Activity Log</h3>
                    <div class="log-controls">
                        <button id="clearLogButton" class="clear-log-button">Clear</button>
                        <button id="exportLogButton" class="export-log-button">Export</button>
                    </div>
                </div>
                <div class="log-container" id="logContainer">
                    <div class="log-entry info">
                        <span class="log-time">00:00:00</span>
                        <span class="log-message">Extension initialized. Ready to start automation.</span>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <button id="pauseButton" class="action-button pause" disabled>
                    <span class="button-icon">⏸</span>
                    Pause
                </button>
                <button id="skipButton" class="action-button skip" disabled>
                    <span class="button-icon">⏭</span>
                    Skip Current
                </button>
                <button id="emergencyStopButton" class="action-button emergency">
                    <span class="button-icon">🛑</span>
                    Emergency Stop
                </button>
            </section>
        </main>

        <footer class="popup-footer">
            <div class="footer-info">
                <span class="version">v1.0</span>
                <span class="separator">•</span>
                <span class="connection-status" id="connectionStatus">Disconnected</span>
            </div>
            <div class="footer-links">
                <a href="#" id="helpLink">Help</a>
                <a href="#" id="aboutLink">About</a>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
