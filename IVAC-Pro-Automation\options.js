/**
 * IVAC Pro Automation - Options Page Script
 * Handles settings management and validation
 */

class OptionsManager {
    constructor() {
        this.form = document.getElementById('settingsForm');
        this.statusMessage = document.getElementById('statusMessage');
        this.saveButton = document.getElementById('saveButton');
        this.testButton = document.getElementById('testConnection');
        this.resetButton = document.getElementById('resetButton');
        
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.bindEvents();
    }

    /**
     * Load saved settings from chrome storage
     */
    async loadSettings() {
        try {
            const settings = await chrome.storage.local.get([
                'apiKey', 'serverUrl', 'mobileNumber', 'password',
                'webFileId', 'applicantName', 'email', 'preferredDate',
                'visaType', 'visitReason', 'ivacCenter', 'retryAttempts',
                'delayBetweenActions', 'enableNotifications'
            ]);

            // Populate form fields with saved values
            Object.keys(settings).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = settings[key] || false;
                    } else {
                        element.value = settings[key] || '';
                    }
                }
            });

            // Set default values if not saved
            this.setDefaults(settings);
        } catch (error) {
            this.showStatus('Error loading settings: ' + error.message, 'error');
        }
    }

    /**
     * Set default values for empty fields
     */
    setDefaults(settings) {
        const defaults = {
            serverUrl: 'https://api.yourserver.com',
            retryAttempts: 3,
            delayBetweenActions: 2000,
            enableNotifications: true,
            visaType: '13',
            ivacCenter: '5'
        };

        Object.keys(defaults).forEach(key => {
            if (!settings[key]) {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = defaults[key];
                    } else {
                        element.value = defaults[key];
                    }
                }
            }
        });
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        this.form.addEventListener('submit', (e) => this.handleSave(e));
        this.testButton.addEventListener('click', () => this.testConnection());
        this.resetButton.addEventListener('click', () => this.resetToDefaults());
        
        // Real-time validation
        const requiredFields = ['apiKey', 'mobileNumber', 'password'];
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', () => this.validateField(field));
            }
        });
    }

    /**
     * Validate individual field
     */
    validateField(field) {
        const value = field.value.trim();
        
        switch (field.id) {
            case 'mobileNumber':
                const phoneRegex = /^01[3-9]\d{8}$/;
                if (value && !phoneRegex.test(value)) {
                    field.setCustomValidity('Please enter a valid Bangladeshi mobile number (01XXXXXXXXX)');
                } else {
                    field.setCustomValidity('');
                }
                break;
            case 'apiKey':
                if (value && value.length < 10) {
                    field.setCustomValidity('API key seems too short');
                } else {
                    field.setCustomValidity('');
                }
                break;
            default:
                field.setCustomValidity('');
        }
    }

    /**
     * Handle form submission
     */
    async handleSave(event) {
        event.preventDefault();
        
        this.saveButton.disabled = true;
        this.saveButton.classList.add('loading');
        this.saveButton.textContent = 'Saving...';

        try {
            const formData = new FormData(this.form);
            const settings = {};

            // Convert form data to object
            for (let [key, value] of formData.entries()) {
                const element = document.getElementById(key);
                if (element && element.type === 'checkbox') {
                    settings[key] = element.checked;
                } else {
                    settings[key] = value.trim();
                }
            }

            // Validate required fields
            const requiredFields = ['apiKey', 'mobileNumber', 'password'];
            const missingFields = requiredFields.filter(field => !settings[field]);
            
            if (missingFields.length > 0) {
                throw new Error(`Please fill in required fields: ${missingFields.join(', ')}`);
            }

            // Save to chrome storage
            await chrome.storage.local.set(settings);
            
            this.showStatus('Settings saved successfully!', 'success');
            
            // Notify background script about settings update
            chrome.runtime.sendMessage({
                action: 'settingsUpdated',
                settings: settings
            });

        } catch (error) {
            this.showStatus('Error saving settings: ' + error.message, 'error');
        } finally {
            this.saveButton.disabled = false;
            this.saveButton.classList.remove('loading');
            this.saveButton.textContent = 'Save Settings';
        }
    }

    /**
     * Test connection to backend server
     */
    async testConnection() {
        this.testButton.disabled = true;
        this.testButton.classList.add('loading');
        this.testButton.textContent = 'Testing...';

        try {
            const apiKey = document.getElementById('apiKey').value.trim();
            const serverUrl = document.getElementById('serverUrl').value.trim();

            if (!apiKey || !serverUrl) {
                throw new Error('Please enter API key and server URL first');
            }

            const response = await fetch(`${serverUrl}/api/auth/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ apiKey })
            });

            const result = await response.json();

            if (response.ok && result.valid) {
                this.showStatus('Connection successful! API key is valid.', 'success');
            } else {
                throw new Error(result.message || 'Invalid API key or server error');
            }

        } catch (error) {
            this.showStatus('Connection failed: ' + error.message, 'error');
        } finally {
            this.testButton.disabled = false;
            this.testButton.classList.remove('loading');
            this.testButton.textContent = 'Test Connection';
        }
    }

    /**
     * Reset form to default values
     */
    async resetToDefaults() {
        if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
            try {
                // Clear storage
                await chrome.storage.local.clear();
                
                // Reset form
                this.form.reset();
                
                // Set defaults
                this.setDefaults({});
                
                this.showStatus('Settings reset to defaults', 'info');
            } catch (error) {
                this.showStatus('Error resetting settings: ' + error.message, 'error');
            }
        }
    }

    /**
     * Show status message
     */
    showStatus(message, type = 'info') {
        this.statusMessage.textContent = message;
        this.statusMessage.className = `status-message ${type}`;
        this.statusMessage.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            this.statusMessage.style.display = 'none';
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new OptionsManager();
});
