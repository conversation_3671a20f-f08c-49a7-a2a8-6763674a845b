<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IVAC Pro Automation - Settings</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>IVAC Pro Automation Settings</h1>
            <p>Configure your automation settings for the IVAC process</p>
        </header>

        <form id="settingsForm">
            <!-- Backend Settings Section -->
            <section class="settings-section">
                <h2>Backend Settings</h2>
                <div class="form-group">
                    <label for="apiKey">API Key:</label>
                    <input type="password" id="apiKey" name="apiKey" placeholder="Enter your API key" required>
                    <small>Your authentication key for the backend server</small>
                </div>
                <div class="form-group">
                    <label for="serverUrl">Server URL:</label>
                    <input type="url" id="serverUrl" name="serverUrl" placeholder="https://api.yourserver.com" value="https://api.yourserver.com">
                    <small>Backend server base URL</small>
                </div>
            </section>

            <!-- IVAC Credentials Section -->
            <section class="settings-section">
                <h2>IVAC Credentials</h2>
                <div class="form-group">
                    <label for="mobileNumber">Mobile Number:</label>
                    <input type="tel" id="mobileNumber" name="mobileNumber" placeholder="01XXXXXXXXX" required>
                    <small>Your registered mobile number for IVAC</small>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" placeholder="Enter your IVAC password" required>
                    <small>Your IVAC account password</small>
                </div>
            </section>

            <!-- Default Application Info Section -->
            <section class="settings-section">
                <h2>Default Application Information</h2>
                <div class="form-group">
                    <label for="webFileId">Web File ID:</label>
                    <input type="text" id="webFileId" name="webFileId" placeholder="BGDKV012DB25">
                    <small>Default web file ID for applications</small>
                </div>
                <div class="form-group">
                    <label for="applicantName">Applicant Name:</label>
                    <input type="text" id="applicantName" name="applicantName" placeholder="JOHN DOE">
                    <small>Default applicant name</small>
                </div>
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>">
                    <small>Default email address</small>
                </div>
                <div class="form-group">
                    <label for="preferredDate">Preferred Appointment Date:</label>
                    <input type="date" id="preferredDate" name="preferredDate">
                    <small>Default preferred appointment date</small>
                </div>
                <div class="form-group">
                    <label for="visaType">Visa Type:</label>
                    <select id="visaType" name="visaType">
                        <option value="13">MEDICAL/MEDICAL ATTENDANT VISA</option>
                        <option value="1">TOURIST VISA</option>
                        <option value="2">BUSINESS VISA</option>
                        <option value="3">STUDENT VISA</option>
                    </select>
                    <small>Default visa type for applications</small>
                </div>
                <div class="form-group">
                    <label for="visitReason">Visit Reason:</label>
                    <input type="text" id="visitReason" name="visitReason" placeholder="For Treatment" maxlength="50">
                    <small>Default reason for visit (max 50 characters)</small>
                </div>
                <div class="form-group">
                    <label for="ivacCenter">IVAC Center:</label>
                    <select id="ivacCenter" name="ivacCenter">
                        <option value="5">Khulna</option>
                        <option value="1">Dhaka</option>
                        <option value="2">Chittagong</option>
                        <option value="3">Sylhet</option>
                        <option value="4">Rajshahi</option>
                    </select>
                    <small>Default IVAC center</small>
                </div>
            </section>

            <!-- Automation Settings Section -->
            <section class="settings-section">
                <h2>Automation Settings</h2>
                <div class="form-group">
                    <label for="retryAttempts">Retry Attempts:</label>
                    <input type="number" id="retryAttempts" name="retryAttempts" min="1" max="10" value="3">
                    <small>Number of retry attempts for failed operations</small>
                </div>
                <div class="form-group">
                    <label for="delayBetweenActions">Delay Between Actions (ms):</label>
                    <input type="number" id="delayBetweenActions" name="delayBetweenActions" min="500" max="5000" value="2000">
                    <small>Delay between automation actions in milliseconds</small>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableNotifications" name="enableNotifications" checked>
                        Enable Notifications
                    </label>
                    <small>Show browser notifications for important events</small>
                </div>
            </section>

            <div class="form-actions">
                <button type="submit" id="saveButton">Save Settings</button>
                <button type="button" id="testConnection">Test Connection</button>
                <button type="button" id="resetButton">Reset to Defaults</button>
            </div>
        </form>

        <div id="statusMessage" class="status-message"></div>
    </div>

    <script src="options.js"></script>
</body>
</html>
