import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import App from './App.jsx'
import './index.css'

// Get the WordPress-provided configuration
const wpConfig = window.ivacDashboard || {
  apiUrl: '/wp-json/ivac/v1/',
  nonce: '',
  currentUser: {},
  pluginUrl: ''
}

// Make config available globally
window.IVAC_CONFIG = wpConfig

ReactDOM.createRoot(document.getElementById('ivac-react-dashboard-root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
