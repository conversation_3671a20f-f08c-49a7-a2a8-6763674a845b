@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 h-10 py-2 px-4;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 h-10 py-2 px-4;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 h-10 py-2 px-4;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-lg {
    @apply h-12 px-6 text-base;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .select {
    @apply input cursor-pointer;
  }
  
  .textarea {
    @apply input min-h-[80px] resize-none;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white p-6 shadow-sm;
  }
  
  .table {
    @apply w-full border-collapse;
  }
  
  .table th {
    @apply border-b border-gray-200 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table td {
    @apply border-b border-gray-100 px-4 py-4 text-sm text-gray-900;
  }
  
  .table tbody tr:hover {
    @apply bg-gray-50;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }

  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }

  .sidebar-nav-item {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors;
  }

  .sidebar-nav-item.active {
    @apply bg-blue-100 text-blue-700;
  }
  
  .sidebar-nav-item:not(.active) {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
  
  .form-group {
    @apply space-y-2;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }
  
  .form-error {
    @apply text-sm text-red-600;
  }
  
  .form-help {
    @apply text-sm text-gray-500;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
