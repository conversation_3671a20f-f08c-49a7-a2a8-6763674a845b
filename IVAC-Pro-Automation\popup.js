/**
 * IVAC Pro Automation - Popup Script
 * Handles the popup interface and communication with background script
 */

class PopupManager {
    constructor() {
        this.isRunning = false;
        this.currentStep = 'login';
        this.startTime = null;
        this.runtimeInterval = null;
        this.stats = {
            jobsProcessed: 0,
            successCount: 0,
            currentJob: 'None'
        };

        this.initializeElements();
        this.bindEvents();
        this.loadInitialState();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        // Buttons
        this.toggleButton = document.getElementById('toggleButton');
        this.settingsButton = document.getElementById('settingsButton');
        this.pauseButton = document.getElementById('pauseButton');
        this.skipButton = document.getElementById('skipButton');
        this.emergencyStopButton = document.getElementById('emergencyStopButton');
        this.clearLogButton = document.getElementById('clearLogButton');
        this.exportLogButton = document.getElementById('exportLogButton');

        // Status elements
        this.statusIndicator = document.getElementById('statusIndicator');
        this.connectionStatus = document.getElementById('connectionStatus');
        this.progressFill = document.getElementById('progressFill');

        // Stats elements
        this.jobsProcessedEl = document.getElementById('jobsProcessed');
        this.successRateEl = document.getElementById('successRate');
        this.currentJobEl = document.getElementById('currentJob');
        this.runtimeEl = document.getElementById('runtime');

        // Log container
        this.logContainer = document.getElementById('logContainer');
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        this.toggleButton.addEventListener('click', () => this.toggleAutomation());
        this.settingsButton.addEventListener('click', () => this.openSettings());
        this.pauseButton.addEventListener('click', () => this.pauseAutomation());
        this.skipButton.addEventListener('click', () => this.skipCurrentJob());
        this.emergencyStopButton.addEventListener('click', () => this.emergencyStop());
        this.clearLogButton.addEventListener('click', () => this.clearLog());
        this.exportLogButton.addEventListener('click', () => this.exportLog());

        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleBackgroundMessage(message);
        });
    }

    /**
     * Load initial state from storage and background script
     */
    async loadInitialState() {
        try {
            // Check if automation is running
            const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
            if (response) {
                this.updateStatus(response);
            }

            // Load stats from storage
            const stored = await chrome.storage.local.get(['stats', 'logs']);
            if (stored.stats) {
                this.stats = { ...this.stats, ...stored.stats };
                this.updateStatsDisplay();
            }

            // Load logs
            if (stored.logs && Array.isArray(stored.logs)) {
                stored.logs.forEach(log => this.addLogEntry(log.type, log.message, log.timestamp));
            }

            // Check connection status
            this.checkConnectionStatus();

        } catch (error) {
            this.addLogEntry('error', 'Failed to load initial state: ' + error.message);
        }
    }

    /**
     * Toggle automation on/off
     */
    async toggleAutomation() {
        try {
            if (this.isRunning) {
                await this.stopAutomation();
            } else {
                await this.startAutomation();
            }
        } catch (error) {
            this.addLogEntry('error', 'Failed to toggle automation: ' + error.message);
        }
    }

    /**
     * Start automation
     */
    async startAutomation() {
        // Check if settings are configured
        const settings = await chrome.storage.local.get(['apiKey', 'mobileNumber', 'password']);
        if (!settings.apiKey || !settings.mobileNumber || !settings.password) {
            this.addLogEntry('error', 'Please configure settings first');
            this.openSettings();
            return;
        }

        const response = await chrome.runtime.sendMessage({ action: 'startAutomation' });
        
        if (response && response.success) {
            this.isRunning = true;
            this.startTime = Date.now();
            this.updateButtonStates();
            this.startRuntimeTimer();
            this.addLogEntry('info', 'Automation started successfully');
            this.updateStatusIndicator('running', 'Running');
        } else {
            this.addLogEntry('error', response?.error || 'Failed to start automation');
        }
    }

    /**
     * Stop automation
     */
    async stopAutomation() {
        const response = await chrome.runtime.sendMessage({ action: 'stopAutomation' });
        
        if (response && response.success) {
            this.isRunning = false;
            this.updateButtonStates();
            this.stopRuntimeTimer();
            this.addLogEntry('info', 'Automation stopped');
            this.updateStatusIndicator('ready', 'Ready');
            this.resetProgress();
        } else {
            this.addLogEntry('error', response?.error || 'Failed to stop automation');
        }
    }

    /**
     * Pause automation
     */
    async pauseAutomation() {
        const response = await chrome.runtime.sendMessage({ action: 'pauseAutomation' });
        
        if (response && response.success) {
            this.addLogEntry('info', 'Automation paused');
            this.updateStatusIndicator('paused', 'Paused');
        }
    }

    /**
     * Skip current job
     */
    async skipCurrentJob() {
        const response = await chrome.runtime.sendMessage({ action: 'skipCurrentJob' });
        
        if (response && response.success) {
            this.addLogEntry('info', 'Current job skipped');
        }
    }

    /**
     * Emergency stop
     */
    async emergencyStop() {
        if (confirm('Are you sure you want to perform an emergency stop? This will immediately halt all operations.')) {
            const response = await chrome.runtime.sendMessage({ action: 'emergencyStop' });
            
            if (response && response.success) {
                this.isRunning = false;
                this.updateButtonStates();
                this.stopRuntimeTimer();
                this.addLogEntry('warning', 'Emergency stop activated');
                this.updateStatusIndicator('stopped', 'Stopped');
                this.resetProgress();
            }
        }
    }

    /**
     * Open settings page
     */
    openSettings() {
        chrome.runtime.openOptionsPage();
    }

    /**
     * Handle messages from background script
     */
    handleBackgroundMessage(message) {
        switch (message.action) {
            case 'statusUpdate':
                this.updateStatus(message.data);
                break;
            case 'logUpdate':
                this.addLogEntry(message.type, message.message);
                break;
            case 'progressUpdate':
                this.updateProgress(message.step, message.progress);
                break;
            case 'statsUpdate':
                this.updateStats(message.stats);
                break;
            case 'jobUpdate':
                this.updateCurrentJob(message.jobId);
                break;
        }
    }

    /**
     * Update automation status
     */
    updateStatus(status) {
        this.isRunning = status.isRunning;
        this.currentStep = status.currentStep || 'login';
        
        if (status.isRunning) {
            this.updateStatusIndicator('running', 'Running');
            if (!this.startTime) {
                this.startTime = Date.now();
                this.startRuntimeTimer();
            }
        } else {
            this.updateStatusIndicator('ready', 'Ready');
            this.stopRuntimeTimer();
        }
        
        this.updateButtonStates();
    }

    /**
     * Update button states based on current status
     */
    updateButtonStates() {
        if (this.isRunning) {
            this.toggleButton.className = 'toggle-button stop';
            this.toggleButton.innerHTML = '<span class="button-icon">⏹</span><span class="button-text">Stop Automation</span>';
            this.pauseButton.disabled = false;
            this.skipButton.disabled = false;
        } else {
            this.toggleButton.className = 'toggle-button start';
            this.toggleButton.innerHTML = '<span class="button-icon">▶</span><span class="button-text">Start Automation</span>';
            this.pauseButton.disabled = true;
            this.skipButton.disabled = true;
        }
    }

    /**
     * Update status indicator
     */
    updateStatusIndicator(status, text) {
        const statusDot = this.statusIndicator.querySelector('.status-dot');
        const statusText = this.statusIndicator.querySelector('.status-text');
        
        statusDot.className = 'status-dot';
        statusText.textContent = text;
        
        switch (status) {
            case 'running':
                statusDot.style.background = '#3b82f6';
                break;
            case 'paused':
                statusDot.style.background = '#f59e0b';
                break;
            case 'error':
                statusDot.style.background = '#ef4444';
                break;
            case 'stopped':
                statusDot.style.background = '#6b7280';
                break;
            default:
                statusDot.style.background = '#10b981';
        }
    }

    /**
     * Update progress display
     */
    updateProgress(step, progress) {
        this.currentStep = step;
        
        // Update progress bar
        this.progressFill.style.width = progress + '%';
        
        // Update step indicators
        const steps = document.querySelectorAll('.step');
        const stepOrder = ['login', 'application', 'payment', 'complete'];
        const currentIndex = stepOrder.indexOf(step);
        
        steps.forEach((stepEl, index) => {
            stepEl.classList.remove('active', 'completed');
            if (index < currentIndex) {
                stepEl.classList.add('completed');
            } else if (index === currentIndex) {
                stepEl.classList.add('active');
            }
        });
    }

    /**
     * Reset progress display
     */
    resetProgress() {
        this.progressFill.style.width = '0%';
        document.querySelectorAll('.step').forEach(step => {
            step.classList.remove('active', 'completed');
        });
    }

    /**
     * Update statistics
     */
    updateStats(newStats) {
        this.stats = { ...this.stats, ...newStats };
        this.updateStatsDisplay();
        
        // Save to storage
        chrome.storage.local.set({ stats: this.stats });
    }

    /**
     * Update stats display
     */
    updateStatsDisplay() {
        this.jobsProcessedEl.textContent = this.stats.jobsProcessed;
        
        const successRate = this.stats.jobsProcessed > 0 
            ? Math.round((this.stats.successCount / this.stats.jobsProcessed) * 100)
            : 0;
        this.successRateEl.textContent = successRate + '%';
        
        this.currentJobEl.textContent = this.stats.currentJob;
    }

    /**
     * Update current job display
     */
    updateCurrentJob(jobId) {
        this.stats.currentJob = jobId || 'None';
        this.currentJobEl.textContent = this.stats.currentJob;
    }

    /**
     * Start runtime timer
     */
    startRuntimeTimer() {
        if (this.runtimeInterval) {
            clearInterval(this.runtimeInterval);
        }
        
        this.runtimeInterval = setInterval(() => {
            if (this.startTime) {
                const elapsed = Date.now() - this.startTime;
                this.runtimeEl.textContent = this.formatTime(elapsed);
            }
        }, 1000);
    }

    /**
     * Stop runtime timer
     */
    stopRuntimeTimer() {
        if (this.runtimeInterval) {
            clearInterval(this.runtimeInterval);
            this.runtimeInterval = null;
        }
    }

    /**
     * Format time in HH:MM:SS
     */
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        return [hours, minutes, secs]
            .map(val => val.toString().padStart(2, '0'))
            .join(':');
    }

    /**
     * Add log entry
     */
    addLogEntry(type, message, timestamp = null) {
        const time = timestamp ? new Date(timestamp) : new Date();
        const timeStr = time.toLocaleTimeString('en-US', { hour12: false });

        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        logEntry.innerHTML = `
            <span class="log-time">${timeStr}</span>
            <span class="log-message">${message}</span>
        `;

        this.logContainer.appendChild(logEntry);
        this.logContainer.scrollTop = this.logContainer.scrollHeight;

        // Keep only last 100 entries
        const entries = this.logContainer.querySelectorAll('.log-entry');
        if (entries.length > 100) {
            entries[0].remove();
        }

        // Save to storage
        this.saveLogsToStorage();
    }

    /**
     * Clear log
     */
    clearLog() {
        this.logContainer.innerHTML = '';
        chrome.storage.local.remove('logs');
        this.addLogEntry('info', 'Log cleared');
    }

    /**
     * Export log
     */
    exportLog() {
        const entries = Array.from(this.logContainer.querySelectorAll('.log-entry'));
        const logText = entries.map(entry => {
            const time = entry.querySelector('.log-time').textContent;
            const message = entry.querySelector('.log-message').textContent;
            const type = entry.className.split(' ')[1];
            return `[${time}] ${type.toUpperCase()}: ${message}`;
        }).join('\n');

        const blob = new Blob([logText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ivac-automation-log-${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        URL.revokeObjectURL(url);
    }

    /**
     * Save logs to storage
     */
    async saveLogsToStorage() {
        const entries = Array.from(this.logContainer.querySelectorAll('.log-entry'));
        const logs = entries.map(entry => ({
            type: entry.className.split(' ')[1],
            message: entry.querySelector('.log-message').textContent,
            timestamp: Date.now()
        }));

        await chrome.storage.local.set({ logs });
    }

    /**
     * Check connection status
     */
    async checkConnectionStatus() {
        try {
            const settings = await chrome.storage.local.get(['apiKey', 'serverUrl']);
            if (!settings.apiKey || !settings.serverUrl) {
                this.updateConnectionStatus(false);
                return;
            }

            const response = await fetch(`${settings.serverUrl}/api/auth/validate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ apiKey: settings.apiKey })
            });

            const result = await response.json();
            this.updateConnectionStatus(response.ok && result.valid);
        } catch (error) {
            this.updateConnectionStatus(false);
        }
    }

    /**
     * Update connection status display
     */
    updateConnectionStatus(connected) {
        this.connectionStatus.textContent = connected ? 'Connected' : 'Disconnected';
        this.connectionStatus.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
