<?php
/**
 * IVAC Dashboard Jobs API - For Chrome Extension Integration
 */

if (!defined('ABSPATH')) {
    exit;
}

class IVAC_Jobs_API {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->register_routes();
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Get next job for processing
        register_rest_route('ivac/v1', '/jobs/next', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_next_job'),
            'permission_callback' => array($this, 'check_api_key_permissions'),
        ));
        
        // Update job status
        register_rest_route('ivac/v1', '/jobs/update', array(
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => array($this, 'update_job_status'),
            'permission_callback' => array($this, 'check_api_key_permissions'),
            'args' => array(
                'jobId' => array(
                    'required' => true,
                    'type' => 'integer',
                    'sanitize_callback' => 'absint',
                ),
                'status' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                    'enum' => array('processing', 'login_failed', 'otp_required', 'captcha_failed', 'completed', 'error', 'skipped'),
                ),
                'paymentLink' => array(
                    'type' => 'string',
                    'sanitize_callback' => 'esc_url_raw',
                ),
                'details' => array(
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_textarea_field',
                ),
                'error' => array(
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_textarea_field',
                ),
            ),
        ));
        
        // Get job statistics
        register_rest_route('ivac/v1', '/jobs/stats', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_job_stats'),
            'permission_callback' => array($this, 'check_api_key_permissions'),
        ));
        
        // Reset job status (for stuck jobs)
        register_rest_route('ivac/v1', '/jobs/(?P<id>\d+)/reset', array(
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => array($this, 'reset_job'),
            'permission_callback' => array($this, 'check_api_key_permissions'),
        ));
    }
    
    /**
     * Check API key permissions
     */
    public function check_api_key_permissions($request) {
        return IVAC_Auth_API::validate_api_key($request);
    }
    
    /**
     * Get next pending job
     */
    public function get_next_job($request) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_files';
        
        // Start transaction
        $wpdb->query('START TRANSACTION');
        
        try {
            // Find next pending job and lock it
            $job = $wpdb->get_row(
                "SELECT * FROM $table_name 
                 WHERE status = 'pending' 
                 ORDER BY created_at ASC 
                 LIMIT 1 
                 FOR UPDATE",
                ARRAY_A
            );
            
            if (!$job) {
                $wpdb->query('COMMIT');
                return new WP_REST_Response(array(
                    'message' => 'No pending jobs.'
                ), 200);
            }
            
            // Update status to processing
            $update_result = $wpdb->update(
                $table_name,
                array(
                    'status' => 'processing',
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $job['id'])
            );
            
            if ($update_result === false) {
                $wpdb->query('ROLLBACK');
                return new WP_Error('db_error', 'Failed to update job status', array('status' => 500));
            }
            
            $wpdb->query('COMMIT');
            
            // Process family members JSON
            if ($job['family_members']) {
                $job['family_members'] = json_decode($job['family_members'], true);
            } else {
                $job['family_members'] = array();
            }
            
            // Prepare application data for Chrome extension
            $application_data = array(
                'webFileId' => $job['web_id'],
                'applicantName' => $job['name'],
                'email' => $job['email'],
                'phone' => $job['phone'],
                'password' => $job['password'],
                'location' => $job['location'],
                'ivacCenter' => $job['ivac_center'],
                'visaType' => $job['visa_type'],
                'visaCategory' => $job['visa_category'],
                'familyMembers' => $job['family_members'],
                'paymentMethod' => $job['payment_method']
            );
            
            return new WP_REST_Response(array(
                'jobId' => (int) $job['id'],
                'applicationData' => $application_data,
                'metadata' => array(
                    'created_at' => $job['created_at'],
                    'attempts' => 0 // You might want to add an attempts column
                )
            ), 200);
            
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return new WP_Error('db_error', 'Database error: ' . $e->getMessage(), array('status' => 500));
        }
    }
    
    /**
     * Update job status
     */
    public function update_job_status($request) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_files';
        
        $job_id = $request->get_param('jobId');
        $status = $request->get_param('status');
        $payment_link = $request->get_param('paymentLink');
        $details = $request->get_param('details');
        $error = $request->get_param('error');
        
        // Check if job exists
        $job = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $job_id), ARRAY_A);
        if (!$job) {
            return new WP_Error('job_not_found', 'Job not found', array('status' => 404));
        }
        
        // Prepare update data
        $update_data = array(
            'status' => $status,
            'updated_at' => current_time('mysql')
        );
        
        // Add payment link if provided
        if ($payment_link) {
            $update_data['payment_link'] = $payment_link;
        }
        
        // Add processed timestamp for completed jobs
        if ($status === 'completed') {
            $update_data['processed_at'] = current_time('mysql');
        }
        
        // Store error details if provided
        if ($error) {
            // You might want to add an errors column to store error details
            $update_data['status'] = 'error';
        }
        
        // Update job
        $result = $wpdb->update($table_name, $update_data, array('id' => $job_id));
        
        if ($result === false) {
            return new WP_Error('db_error', 'Failed to update job status', array('status' => 500));
        }
        
        // Log the status change (you might want to implement a logging system)
        $this->log_job_status_change($job_id, $job['status'], $status, $details, $error);
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Job status updated successfully',
            'jobId' => $job_id,
            'status' => $status,
            'updated_at' => current_time('mysql')
        ), 200);
    }
    
    /**
     * Get job statistics
     */
    public function get_job_stats($request) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_files';
        
        // Get counts by status
        $stats = $wpdb->get_results(
            "SELECT status, COUNT(*) as count FROM $table_name GROUP BY status",
            ARRAY_A
        );
        
        // Get today's stats
        $today_stats = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT status, COUNT(*) as count FROM $table_name 
                 WHERE DATE(created_at) = %s 
                 GROUP BY status",
                current_time('Y-m-d')
            ),
            ARRAY_A
        );
        
        // Get processing time averages for completed jobs
        $avg_processing_time = $wpdb->get_var(
            "SELECT AVG(TIMESTAMPDIFF(MINUTE, created_at, processed_at)) 
             FROM $table_name 
             WHERE status = 'completed' AND processed_at IS NOT NULL"
        );
        
        // Format stats
        $formatted_stats = array();
        $today_formatted_stats = array();
        
        foreach ($stats as $stat) {
            $formatted_stats[$stat['status']] = (int) $stat['count'];
        }
        
        foreach ($today_stats as $stat) {
            $today_formatted_stats[$stat['status']] = (int) $stat['count'];
        }
        
        return new WP_REST_Response(array(
            'total_stats' => $formatted_stats,
            'today_stats' => $today_formatted_stats,
            'avg_processing_time_minutes' => $avg_processing_time ? round($avg_processing_time, 2) : 0,
            'generated_at' => current_time('mysql')
        ), 200);
    }
    
    /**
     * Reset job status (for stuck jobs)
     */
    public function reset_job($request) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_files';
        
        $job_id = $request->get_param('id');
        
        // Check if job exists and is in processing state
        $job = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND status = 'processing'",
            $job_id
        ), ARRAY_A);
        
        if (!$job) {
            return new WP_Error('job_not_found', 'Job not found or not in processing state', array('status' => 404));
        }
        
        // Reset to pending
        $result = $wpdb->update(
            $table_name,
            array(
                'status' => 'pending',
                'updated_at' => current_time('mysql')
            ),
            array('id' => $job_id)
        );
        
        if ($result === false) {
            return new WP_Error('db_error', 'Failed to reset job', array('status' => 500));
        }
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Job reset to pending status',
            'jobId' => $job_id
        ), 200);
    }
    
    /**
     * Log job status changes
     */
    private function log_job_status_change($job_id, $old_status, $new_status, $details = null, $error = null) {
        // You can implement a logging system here
        // For now, we'll just use WordPress's built-in logging
        
        $log_message = sprintf(
            'Job %d status changed from %s to %s',
            $job_id,
            $old_status,
            $new_status
        );
        
        if ($details) {
            $log_message .= ' - Details: ' . $details;
        }
        
        if ($error) {
            $log_message .= ' - Error: ' . $error;
        }
        
        error_log('[IVAC Dashboard] ' . $log_message);
    }
}
