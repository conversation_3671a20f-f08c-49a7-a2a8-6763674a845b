import axios from 'axios'

// Get WordPress configuration
const config = window.IVAC_CONFIG || {}

// Create axios instance
const api = axios.create({
  baseURL: config.apiUrl || '/wp-json/ivac/v1/',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor to add nonce
api.interceptors.request.use(
  (config) => {
    // Add WordPress nonce for authentication
    if (window.IVAC_CONFIG?.nonce) {
      config.headers['X-WP-Nonce'] = window.IVAC_CONFIG.nonce
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Handle common errors
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          console.error('Authentication failed')
          break
        case 403:
          console.error('Permission denied')
          break
        case 404:
          console.error('Resource not found')
          break
        case 500:
          console.error('Server error')
          break
        default:
          console.error('API Error:', data?.message || error.message)
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message)
    } else {
      // Other error
      console.error('Error:', error.message)
    }
    
    return Promise.reject(error)
  }
)

// API methods
export const filesAPI = {
  // Get all files
  getFiles: (params = {}) => api.get('/files', { params }),
  
  // Get single file
  getFile: (id) => api.get(`/files/${id}`),
  
  // Create file
  createFile: (data) => api.post('/files', data),
  
  // Update file
  updateFile: (id, data) => api.put(`/files/${id}`, data),
  
  // Delete file
  deleteFile: (id) => api.delete(`/files/${id}`),
}

export const authAPI = {
  // Validate API key
  validateKey: (apiKey) => api.post('/auth', { apiKey }),
  
  // Generate new API key
  generateKey: (name) => api.post('/auth/generate-key', { name }),
  
  // Get API keys
  getKeys: () => api.get('/auth/keys'),
  
  // Revoke API key
  revokeKey: (id) => api.delete(`/auth/keys/${id}`),
}

export const jobsAPI = {
  // Get next job
  getNextJob: () => api.get('/jobs/next'),
  
  // Update job status
  updateJob: (data) => api.post('/jobs/update', data),
  
  // Get job statistics
  getStats: () => api.get('/jobs/stats'),
  
  // Reset job
  resetJob: (id) => api.post(`/jobs/${id}/reset`),
}

export default api
