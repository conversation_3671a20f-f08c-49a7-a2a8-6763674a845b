const originalLog = console.log
  , originalErr = console.error;
console.log = function(...o) {
    const n = (new Date).toLocaleTimeString();
    originalLog(`[${n}]`, ...o)
}
,
console.error = function(...o) {
    const n = (new Date).toLocaleTimeString();
    originalErr(`[${n}]`, ...o)
}
;
const file = {
    "thaiInfo": [],
    "content": "_token=TOKEN&apiKey=TOKEN&action=sendOtp&info%5B0%5D%5Bweb_id%5D=BGDKV012DB25&info%5B0%5D%5Bname%5D=NASIMA%20BEGUM&info%5B0%5D%5Bemail%5D=NASIMABEGUM2134%40GMAIL.COM&info%5B0%5D%5Bphone%5D=01810448193&info%5B0%5D%5BamountChangeData%5D%5Ballow_old_amount_until_new_date%5D=2&info%5B0%5D%5BamountChangeData%5D%5Bmax_notification_count%5D=0&info%5B0%5D%5BamountChangeData%5D%5Bold_visa_fees%5D=800.00&info%5B0%5D%5BamountChangeData%5D%5Bnew_fees_applied_from%5D=2018-08-05%2000%3A00%3A00&info%5B0%5D%5BamountChangeData%5D%5Bnotice%5D=false&info%5B0%5D%5BamountChangeData%5D%5Bnotice_short%5D=&info%5B0%5D%5BamountChangeData%5D%5Bnotice_popup%5D=&info%5B0%5D%5BamountChangeData%5D%5Bnew_visa_fee%5D=800.00&info%5B0%5D%5Bpassport%5D=&info%5B0%5D%5Bweb_id_repeat%5D=BGDKV012DB25&info%5B0%5D%5Bamount%5D=800.00&info%5B0%5D%5Bcaptcha%5D=&info%5B0%5D%5Bcenter%5D%5Bid%5D=5&info%5B0%5D%5Bcenter%5D%5Bc_name%5D=Khulna&info%5B0%5D%5Bcenter%5D%5Bprefix%5D=K&info%5B0%5D%5Bcenter%5D%5Bis_delete%5D=0&info%5B0%5D%5Bcenter%5D%5Bcreated_by%5D=&info%5B0%5D%5Bcenter%5D%5Bcreated_at%5D=2019-06-24%2012%3A08%3A36&info%5B0%5D%5Bcenter%5D%5Bupdated_at%5D=2019-06-24%2012%3A08%3A36&info%5B0%5D%5Bis_open%5D=false&info%5B0%5D%5Bivac%5D%5Bid%5D=3&info%5B0%5D%5Bivac%5D%5Bcenter_info_id%5D=5&info%5B0%5D%5Bivac%5D%5Bivac_name%5D=IVAC%2C%20KHULNA&info%5B0%5D%5Bivac%5D%5Baddress%5D=Dr.%20Motiar%20Rahman%20Tower%2C64%2C%20KDA%20Avenue%2CKDA%20Commercial%20Area%2CBanking%20Zone%2C%20Khulna-9100.&info%5B0%5D%5Bivac%5D%5Bprefix%5D=D&info%5B0%5D%5Bivac%5D%5Bceated_on%5D=2017-08-30%2019%3A07%3A08&info%5B0%5D%5Bivac%5D%5Bvisa_fee%5D=800.00&info%5B0%5D%5Bivac%5D%5Bis_delete%5D=0&info%5B0%5D%5Bivac%5D%5Bcreated_at%5D=&info%5B0%5D%5Bivac%5D%5Bupdated_at%5D=&info%5B0%5D%5Bivac%5D%5Bapp_key%5D=IVACKHULNA&info%5B0%5D%5Bivac%5D%5Bcontact_number%5D=&info%5B0%5D%5Bivac%5D%5Bcreated_by%5D=&info%5B0%5D%5Bivac%5D%5Bcharge%5D=3&info%5B0%5D%5Bivac%5D%5Bnew_visa_fee%5D=800.00&info%5B0%5D%5Bivac%5D%5Bold_visa_fee%5D=800.00&info%5B0%5D%5Bivac%5D%5Bnew_fees_applied_from%5D=2018-08-05%2000%3A00%3A00&info%5B0%5D%5Bivac%5D%5Bnotify_fees_from%5D=2018-07-29%2004%3A54%3A32&info%5B0%5D%5Bivac%5D%5Bmax_notification_count%5D=2&info%5B0%5D%5Bivac%5D%5Ballow_old_amount_until_new_date%5D=2&info%5B0%5D%5Bivac%5D%5Bnotification_text_beside_amount%5D=%28From%20%3Cfrom%3E%20this%20IVAC%20fees%20will%20be%20%3Cnew_amount%3E%20BDT%29&info%5B0%5D%5Bivac%5D%5Bnotification_text_popup%5D=&info%5B0%5D%5Bvisa_type%5D%5Bid%5D=13&info%5B0%5D%5Bvisa_type%5D%5Btype_name%5D=MEDICAL%2FMEDICAL%20ATTENDANT%20VISA&info%5B0%5D%5Bvisa_type%5D%5Border%5D=2&info%5B0%5D%5Bvisa_type%5D%5Bis_active%5D=1&info%5B0%5D%5Bvisa_type%5D%5B%24%24hashKey%5D=object%3A50&info%5B0%5D%5Botp%5D=&info%5B0%5D%5Bconfirm_tos%5D=true&info%5B1%5D%5Bweb_id%5D=BGDKV012DF25&info%5B1%5D%5Bname%5D=MD%20FORKAN%20HOWLADER&info%5B1%5D%5Bphone%5D=01810448193&info%5B1%5D%5Bemail%5D=NASIMABEGUM2134%40GMAIL.COM&info%5B1%5D%5BamountChangeData%5D%5Ballow_old_amount_until_new_date%5D=2&info%5B1%5D%5BamountChangeData%5D%5Bmax_notification_count%5D=0&info%5B1%5D%5BamountChangeData%5D%5Bold_visa_fees%5D=800.00&info%5B1%5D%5BamountChangeData%5D%5Bnew_fees_applied_from%5D=2018-08-05%2000%3A00%3A00&info%5B1%5D%5BamountChangeData%5D%5Bnotice%5D=false&info%5B1%5D%5BamountChangeData%5D%5Bnotice_short%5D=&info%5B1%5D%5BamountChangeData%5D%5Bnotice_popup%5D=&info%5B1%5D%5BamountChangeData%5D%5Bnew_visa_fee%5D=800.00&info%5B1%5D%5Bpassport%5D=&info%5B1%5D%5Bweb_id_repeat%5D=BGDKV012DF25&info%5B1%5D%5Bamount%5D=800.00&info%5B1%5D%5Bcaptcha%5D=&info%5B1%5D%5Bcenter%5D%5Bid%5D=5&info%5B1%5D%5Bcenter%5D%5Bc_name%5D=Khulna&info%5B1%5D%5Bcenter%5D%5Bprefix%5D=K&info%5B1%5D%5Bcenter%5D%5Bis_delete%5D=0&info%5B1%5D%5Bcenter%5D%5Bcreated_by%5D=&info%5B1%5D%5Bcenter%5D%5Bcreated_at%5D=2019-06-24%2012%3A08%3A36&info%5B1%5D%5Bcenter%5D%5Bupdated_at%5D=2019-06-24%2012%3A08%3A36&info%5B1%5D%5Bis_open%5D=false&info%5B1%5D%5Bivac%5D%5Bid%5D=3&info%5B1%5D%5Bivac%5D%5Bcenter_info_id%5D=5&info%5B1%5D%5Bivac%5D%5Bivac_name%5D=IVAC%2C%20KHULNA&info%5B1%5D%5Bivac%5D%5Baddress%5D=Dr.%20Motiar%20Rahman%20Tower%2C64%2C%20KDA%20Avenue%2CKDA%20Commercial%20Area%2CBanking%20Zone%2C%20Khulna-9100.&info%5B1%5D%5Bivac%5D%5Bprefix%5D=D&info%5B1%5D%5Bivac%5D%5Bceated_on%5D=2017-08-30%2019%3A07%3A08&info%5B1%5D%5Bivac%5D%5Bvisa_fee%5D=800.00&info%5B1%5D%5Bivac%5D%5Bis_delete%5D=0&info%5B1%5D%5Bivac%5D%5Bcreated_at%5D=&info%5B1%5D%5Bivac%5D%5Bupdated_at%5D=&info%5B1%5D%5Bivac%5D%5Bapp_key%5D=IVACKHULNA&info%5B1%5D%5Bivac%5D%5Bcontact_number%5D=&info%5B1%5D%5Bivac%5D%5Bcreated_by%5D=&info%5B1%5D%5Bivac%5D%5Bcharge%5D=3&info%5B1%5D%5Bivac%5D%5Bnew_visa_fee%5D=800.00&info%5B1%5D%5Bivac%5D%5Bold_visa_fee%5D=800.00&info%5B1%5D%5Bivac%5D%5Bnew_fees_applied_from%5D=2018-08-05%2000%3A00%3A00&info%5B1%5D%5Bivac%5D%5Bnotify_fees_from%5D=2018-07-29%2004%3A54%3A32&info%5B1%5D%5Bivac%5D%5Bmax_notification_count%5D=2&info%5B1%5D%5Bivac%5D%5Ballow_old_amount_until_new_date%5D=2&info%5B1%5D%5Bivac%5D%5Bnotification_text_beside_amount%5D=%28From%20%3Cfrom%3E%20this%20IVAC%20fees%20will%20be%20%3Cnew_amount%3E%20BDT%29&info%5B1%5D%5Bivac%5D%5Bnotification_text_popup%5D=&info%5B1%5D%5Bvisa_type%5D%5Bid%5D=13&info%5B1%5D%5Bvisa_type%5D%5Btype_name%5D=MEDICAL%2FMEDICAL%20ATTENDANT%20VISA&info%5B1%5D%5Bvisa_type%5D%5Border%5D=2&info%5B1%5D%5Bvisa_type%5D%5Bis_active%5D=1&info%5B1%5D%5Bvisa_type%5D%5B%24%24hashKey%5D=object%3A50&info%5B1%5D%5Botp%5D=&info%5B1%5D%5Bconfirm_tos%5D=true&resend=0&selected_payment%5Bname%5D=CITYTOUCH&selected_payment%5Bslug%5D=city&selected_payment%5Bgrand_total%5D=1648&selected_payment%5Blink%5D=https%3A%2F%2Fsecurepay.sslcommerz.com%2Fgwprocess%2Fv4%2Fimage%2Fgw1%2Fcitytouch.png&selected_payment%5Bnumber%5D=01970787776",
    "otpPhone": "01810448193",
    "bgd1": "BGDKV012DB25",
    "bgd2": "BGDKV012DF25",
    "bgd3": "",
    "bgd4": "",
    "bgd5": "",
    "totalBGD": 2,
    "visaType": "MEDICAL/MEDICAL ATTENDANT VISA",
    "mission": "Khulna",
    "linkSent": "NO",
    "paymentNumber": "01970787776",
    "paymentLink": "",
    "visaFee": "",
    "qrCode": "",
    "password": "nasima1122",
    "visitReason": "For Treatment",
    "paymentStatus": "",
    "paymentMethod": "city",
    "appoinmentDateTime": "",
    "user": "bitu.vi",
    "ref": ["redwan", "tomal", "nabyalam", "mithundatto", "bitu.vi"],
    "lastModified": {
        "_seconds": **********,
        "_nanoseconds": 437000000
    },
    "priority": "urgent"
}
let hitTime = {
    "hour": 8,
    "min": 59,
    "sec": 59
};
const dhakaTime = "17:27";
const defaultApptDate = "2025-02-18";
const processingDay = "2025-02-17";
const baseUrl = 'https://payment.ivacbd.com';
const siteKey = '6LdOCpAqAAAAAOLNB3Vwt_H7Nw4GGCAbdYm5Brsb';
const pageUrl = 'https://payment.ivacbd.com/';
const API_KEY = 'CAP-D482812E56285C1B5F517694A2E8BB2F123748C2FA20A99EFA92826B0BD1C042';
const payInvoiceNumThreadPerMin = 40;
const xdelay = 2000;
const xthread = 30;

let script = document.createElement('script');
script.src = 'https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js?t=1739791650907';
script.onload = () => {
    console.log('axios loaded!');
    let scriptmoment = document.createElement('script');
    scriptmoment.src = 'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js?t=1739791650907';
    scriptmoment.onload = () => {
        console.log('moment loaded!');
    }
    ;
    document.head.appendChild(scriptmoment);

    let script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/qs@6.13.1/dist/qs.js?t=1739791650907';
    script.onload = () => {
        console.log('qs loaded!');
        window.qs = Qs;
        const a0_0xf97be7 = a0_0x4aba;
        (function(_0x514f7b, _0x3d3366) {
            const _0x3099f2 = a0_0x4aba
              , _0x285068 = _0x514f7b();
            while (!![]) {
                try {
                    const _0x34b854 = -parseInt(_0x3099f2(0x1f7)) / 0x1 * (-parseInt(_0x3099f2(0x190)) / 0x2) + -parseInt(_0x3099f2(0x1fb)) / 0x3 * (-parseInt(_0x3099f2(0x184)) / 0x4) + parseInt(_0x3099f2(0x149)) / 0x5 + parseInt(_0x3099f2(0x1bf)) / 0x6 * (parseInt(_0x3099f2(0x179)) / 0x7) + parseInt(_0x3099f2(0x19b)) / 0x8 * (-parseInt(_0x3099f2(0x1a6)) / 0x9) + -parseInt(_0x3099f2(0x1c1)) / 0xa * (parseInt(_0x3099f2(0x19c)) / 0xb) + parseInt(_0x3099f2(0x15e)) / 0xc * (-parseInt(_0x3099f2(0x18a)) / 0xd);
                    if (_0x34b854 === _0x3d3366)
                        break;
                    else
                        _0x285068['push'](_0x285068['shift']());
                } catch (_0x11bdac) {
                    _0x285068['push'](_0x285068['shift']());
                }
            }
        }(a0_0x22cd, 0x5110d));
        const defaultPaymentMethod = {
            'name': a0_0xf97be7(0x1ae),
            'slug': a0_0xf97be7(0x120),
            'link': a0_0xf97be7(0x156)
        };
        function delay(_0x7126b7) {
            return new Promise(_0x59560b => setTimeout(_0x59560b, _0x7126b7));
        }
        async function updateStatus(_0x236181, _0x66c0d1) {
            const _0x5202f7 = a0_0xf97be7;
            try {
                await axios[_0x5202f7(0x1c3)](_0x5202f7(0x131) + _0x236181 + '&status=' + _0x66c0d1);
            } catch (_0x1a75eb) {
                console['error'](_0x5202f7(0x1b6), _0x1a75eb[_0x5202f7(0x18b)]);
            }
        }
        async function putTimeSlot(_0x39b2a2) {
            const _0x35da1b = a0_0xf97be7;
            try {
                await axios[_0x35da1b(0x1c3)](_0x35da1b(0x1b3) + qs['stringify'](_0x39b2a2));
            } catch (_0x36b610) {
                console['error']('network\x20error\x20while\x20updating\x20status:', _0x36b610['message']);
            }
        }
        async function getTimeSlot(_0x211ce8, _0x498945) {
            const _0x101340 = a0_0xf97be7;
            try {
                const _0x5b0eb2 = await axios[_0x101340(0x1c3)](_0x101340(0x16e) + _0x211ce8 + _0x101340(0x17d) + _0x498945);
                if (_0x5b0eb2['data'] && _0x5b0eb2['data'][_0x101340(0x1b2)])
                    return _0x5b0eb2['data'];
            } catch (_0x52ba50) {
                console[_0x101340(0x1f9)](_0x101340(0x1b6), _0x52ba50[_0x101340(0x18b)]);
            }
        }
        async function getTxData() {
            const _0x259498 = a0_0xf97be7;
            try {
                const _0x3e17b9 = await axios[_0x259498(0x1c3)](_0x259498(0x152) + file[_0x259498(0x145)]);
                if (_0x3e17b9[_0x259498(0x1fc)] && _0x3e17b9[_0x259498(0x1fc)]['transaction'])
                    return _0x3e17b9[_0x259498(0x1fc)][_0x259498(0x16c)];
            } catch (_0x21bb4d) {
                console[_0x259498(0x1f9)](_0x259498(0x1af), _0x21bb4d[_0x259498(0x18b)]);
            }
            return ![];
        }
        function a0_0x4aba(_0xfedae3, _0x1ccad9) {
            const _0x22cdcf = a0_0x22cd();
            return a0_0x4aba = function(_0x4aba59, _0x56945e) {
                _0x4aba59 = _0x4aba59 - 0x11e;
                let _0x3086ff = _0x22cdcf[_0x4aba59];
                return _0x3086ff;
            }
            ,
            a0_0x4aba(_0xfedae3, _0x1ccad9);
        }
        async function updateFile(_0x4d7309) {
            const _0x553361 = a0_0xf97be7;
            try {
                await axios['get']('https://entry-458898283632.us-central1.run.app/update-file?' + qs['stringify'](_0x4d7309));
            } catch (_0x3634de) {
                console[_0x553361(0x1f9)](_0x553361(0x1b6), _0x3634de[_0x553361(0x18b)]);
            }
        }
        let terminate = ![];
        async function shouldTerminate() {
            const _0x3a2c32 = a0_0xf97be7;
            try {
                const _0x2c093c = await (await fetch(_0x3a2c32(0x131) + file[_0x3a2c32(0x145)]))[_0x3a2c32(0x1a4)]();
                terminate = !!_0x2c093c[_0x3a2c32(0x1d2)];
            } catch (_0x2513fd) {
                console[_0x3a2c32(0x1f9)](_0x3a2c32(0x1b4), _0x2513fd);
            }
        }
        setInterval(shouldTerminate, 0x2710);
        async function shouldPause(_0x1fd230, _0x55905a, _0x2e04e7) {
            const _0x58c900 = a0_0xf97be7;
            try {
                const _0x145d6c = await axios[_0x58c900(0x1c3)](_0x58c900(0x131) + _0x1fd230 + _0x58c900(0x121) + _0x55905a + _0x58c900(0x132) + _0x2e04e7);
                if (_0x145d6c['data'][_0x58c900(0x19f)])
                    return !![];
            } catch (_0x3bc857) {
                console[_0x58c900(0x1f9)]('network\x20error\x20while\x20updating\x20status:', _0x3bc857['message']);
            }
            return ![];
        }
        async function handleProcessTransaction() {
            const _0x345f48 = a0_0xf97be7;
            let _0x4c2603 = await getTxData();
            if (_0x4c2603 === ![]) {
                setTimeout(handleProcessTransaction, 0x2710),
                console['log'](_0x345f48(0x1a7));
                return;
            }
            let _0x21f13e = 0x1e, _0x5b21bd;
            await processTransaction(_0x4c2603, _0x21f13e, async _0x17420f => {
                const _0x257164 = _0x345f48;
                _0x5b21bd = _0x17420f;
                if (_0x5b21bd == TERMINATE || _0x5b21bd && typeof _0x5b21bd == _0x257164(0x1ad) && _0x5b21bd[_0x257164(0x124)]('ERROR'))
                    return;
                await updateFile({
                    'otpPhone': file['otpPhone'],
                    'invoiceLink': _0x5b21bd
                }),
                await checkResult();
            }
            );
        }
        async function checkResult() {
            const _0x177fbd = a0_0xf97be7;
            let _0x42488f = 'NO';
            for (let _0x50eeae = 0x1; _0x50eeae <= 0x5; _0x50eeae++) {
                let _0x542b79 = file['bgd' + _0x50eeae] && file[_0x177fbd(0x1b8) + _0x50eeae][_0x177fbd(0x1b0)]();
                if (!_0x542b79)
                    continue;
                let _0x1b9269 = await stepNext(axios, _0x177fbd(0x166) + _0x542b79);
                if (_0x1b9269 != _0x177fbd(0x19d)) {
                    _0x42488f = 'YES';
                    break;
                }
            }
            updateFile({
                'otpPhone': file[_0x177fbd(0x145)],
                'paymentStatus': _0x42488f
            });
            if (_0x42488f == 'NO') {
                console[_0x177fbd(0x164)](_0x177fbd(0x1f2)),
                setTimeout(restartFromBegining, 0x3e8);
                return;
            }
        }
        async function stepNext(_0x2eae48, _0x42ca53) {
            const _0x499759 = a0_0xf97be7;
            console['log'](_0x499759(0x1ab) + _0x42ca53);
            const _0x4a5a6b = '' + baseUrl + _0x42ca53;
            for (let _0x1e72ee = 0x1; !![]; _0x1e72ee++) {
                try {
                    const _0x16b902 = await _0x2eae48[_0x499759(0x1c3)](_0x4a5a6b);
                    return _0x16b902[_0x499759(0x128)] == 0xc8 ? (console[_0x499759(0x164)](_0x42ca53 + _0x499759(0x1c7) + _0x1e72ee, _0x16b902[_0x499759(0x128)], _0x16b902[_0x499759(0x1fc)]),
                    _0x16b902[_0x499759(0x1fc)] === ![] ? 'PASS' : _0x499759(0x170)) : _0x499759(0x1da);
                } catch (_0x5089b5) {
                    console['log'](_0x42ca53 + _0x499759(0x1c7) + _0x1e72ee + _0x499759(0x13e) + _0x5089b5);
                }
                await delay(xdelay);
            }
        }
        async function processTransaction(_0x31727c, _0x3ba40e, _0x1c23e6) {
            const _0x25559e = a0_0xf97be7
              , _0x5199a3 = 0xea60 / _0x3ba40e
              , _0x1cc2a0 = baseUrl + _0x25559e(0x1cf);
            let _0xa77d6c = ![];
            const _0x4d8204 = async _0x5e26eb => {
                const _0x1319a5 = _0x25559e;
                try {
                    const _0x2b3320 = await axios[_0x1319a5(0x1e4)](_0x1cc2a0, _0x31727c, {
                        'maxRedirects': 0x0,
                        'validateStatus': _0xff4f56 => _0xff4f56 === 0x12e,
                        'headers': {
                            'accept': _0x1319a5(0x13c),
                            'accept-language': _0x1319a5(0x172),
                            'content-type': _0x1319a5(0x1a0)
                        }
                    });
                    if (_0xa77d6c)
                        return console['log'](_0x1319a5(0x1d5)),
                        ![];
                    const _0x3f47ab = _0x2b3320['headers']['location'];
                    return console['log']('invoice\x20url:', _0x3f47ab),
                    _0x3f47ab;
                } catch (_0x412c7e) {
                    if (!_0xa77d6c) {
                        console['log'](_0x1319a5(0x1a9) + _0x5e26eb + _0x1319a5(0x13e) + _0x412c7e);
                        if (_0x412c7e[_0x1319a5(0x199)] && _0x412c7e[_0x1319a5(0x199)][_0x1319a5(0x128)] === 0x12e) {
                            const _0x403911 = _0x412c7e[_0x1319a5(0x199)][_0x1319a5(0x183)]['location'];
                            return console[_0x1319a5(0x164)](_0x1319a5(0x182), _0x403911),
                            _0x403911;
                        } else
                            throw _0x412c7e;
                    }
                }
            }
            ;
            do {
                for (let _0x30c0cc = 0x0; _0x30c0cc < _0x3ba40e && !_0xa77d6c; _0x30c0cc++) {
                    _0x4d8204(_0x30c0cc)[_0x25559e(0x18f)](_0x3118ef => {
                        const _0x14d5af = _0x25559e;
                        console[_0x14d5af(0x164)](_0x14d5af(0x157), _0x3118ef),
                        !_0xa77d6c && _0x3118ef && (_0xa77d6c = !![],
                        _0x1c23e6(_0x3118ef));
                    }
                    )[_0x25559e(0x16a)](_0x126983 => {
                        const _0x23941c = _0x25559e;
                        if (!_0xa77d6c) {
                            if (_0x126983[_0x23941c(0x128)] == 0xc8) {
                                _0xa77d6c = !![];
                                const _0x47563b = _0x126983[_0x23941c(0x1c0)][_0x23941c(0x1db)];
                                console[_0x23941c(0x164)](_0x23941c(0x1ba), _0x47563b),
                                _0x1c23e6(_0x47563b);
                            } else
                                console[_0x23941c(0x1f9)](_0x23941c(0x157), _0x126983[_0x23941c(0x128)]);
                        }
                    }
                    ),
                    await delay(_0x5199a3);
                }
                terminate && !_0xa77d6c && (_0xa77d6c = !![],
                _0x1c23e6(TERMINATE));
            } while (!_0xa77d6c);
        }
        async function callIvacAjax(_0x1625ea=a0_0xf97be7(0x13a), _0x1f78f0=a0_0xf97be7(0x13a), _0xd23856=async () => {
            return {};
        }
        , _0x47ed82=0x3, _0x98c4e7, _0x10ea43) {
            const _0x3b49d7 = a0_0xf97be7;
            let _0x366f98 = ![]
              , _0x2bc506 = ![]
              , _0x3a8b78 = ![];
            const _0x510847 = async _0x4c8187 => {
                const _0x513fe4 = a0_0x4aba;
                try {
                    if (terminate)
                        return _0x513fe4(0x17e);
                    if (_0x366f98 || _0x2bc506)
                        return _0x513fe4(0x17e);
                    let _0x3ee4ff = {
                        'headers': {
                            'accept': _0x513fe4(0x138),
                            'accept-language': 'en-US,en;q=0.9',
                            'content-type': 'application/x-www-form-urlencoded;\x20charset=UTF-8',
                            'priority': _0x513fe4(0x1e9),
                            'sec-ch-ua': _0x513fe4(0x168),
                            'sec-ch-ua-mobile': '?0',
                            'sec-ch-ua-platform': '\x22Windows\x22',
                            'sec-fetch-dest': _0x513fe4(0x143),
                            'sec-fetch-mode': _0x513fe4(0x139),
                            'sec-fetch-site': _0x513fe4(0x1a5),
                            'x-requested-with': 'XMLHttpRequest'
                        },
                        'referrer': _0x1f78f0,
                        'referrerPolicy': _0x513fe4(0x12a),
                        'mode': _0x513fe4(0x139),
                        'credentials': _0x513fe4(0x135),
                        'method': _0x513fe4(0x1fa),
                        'body': qs[_0x513fe4(0x1d0)](await _0xd23856())
                    };
                    const _0x28e1d3 = performance[_0x513fe4(0x197)]()
                      , _0x29b179 = await fetch(_0x1625ea, _0x3ee4ff)
                      , _0x13c39f = performance[_0x513fe4(0x197)]();
                    if (_0x29b179[_0x513fe4(0x128)] === 0x1a3) {
                        let _0x44b03c = await _0x29b179[_0x513fe4(0x1a4)]();
                        if (_0x44b03c[_0x513fe4(0x18b)] && _0x44b03c[_0x513fe4(0x18b)][_0x513fe4(0x124)](_0x513fe4(0x1ce)))
                            return console[_0x513fe4(0x164)](_0x1625ea + _0x513fe4(0x1f1) + _0x4c8187 + _0x513fe4(0x146) + _0x29b179[_0x513fe4(0x128)] + _0x513fe4(0x150) + (_0x13c39f - _0x28e1d3)['toFixed'](0x3) + _0x513fe4(0x11e) + JSON[_0x513fe4(0x1d0)](_0x44b03c)),
                            _0x3a8b78 = !![],
                            'session_expired';
                    }
                    if (_0x29b179['status'] === 0xc8) {
                        let _0x3144fb = await _0x29b179[_0x513fe4(0x1a4)]();
                        return _0x3144fb[_0x513fe4(0x1c6)] == !![] && !_0x366f98 && (_0x366f98 = _0x3144fb),
                        _0x3144fb[_0x513fe4(0x1c6)] == ![] && !_0x2bc506 && (_0x2bc506 = _0x3144fb),
                        console[_0x513fe4(0x164)](_0x1625ea + _0x513fe4(0x1f1) + _0x4c8187 + '\x20status:\x20' + _0x29b179['status'] + _0x513fe4(0x150) + (_0x13c39f - _0x28e1d3)['toFixed'](0x3) + '\x20ms.\x20response:\x20' + JSON[_0x513fe4(0x1d0)](_0x3144fb)),
                        'ok';
                    }
                    return _0x29b179[_0x513fe4(0x1aa)][_0x513fe4(0x19e)](),
                    console[_0x513fe4(0x164)](_0x1625ea + '\x20attempt\x20' + _0x4c8187 + '\x20status:\x20' + _0x29b179[_0x513fe4(0x128)] + _0x513fe4(0x150) + (_0x13c39f - _0x28e1d3)[_0x513fe4(0x129)](0x3) + _0x513fe4(0x193)),
                    _0x513fe4(0x1f9);
                } catch (_0x2a5433) {
                    if (!_0x366f98)
                        return console[_0x513fe4(0x164)](_0x1625ea + _0x513fe4(0x1c7) + _0x4c8187 + _0x513fe4(0x13e) + _0x2a5433),
                        _0x513fe4(0x1f9);
                }
            }
            ;
            for (let _0x4d0dbd = 0x0; !terminate; _0x4d0dbd++) {
                _0x4d0dbd % _0x47ed82 == 0x0 && _0x510847(_0x4d0dbd)[_0x3b49d7(0x18f)](_0x5c5836 => {
                    const _0x2a0769 = _0x3b49d7;
                    console[_0x2a0769(0x164)](_0x2a0769(0x14a), _0x5c5836);
                }
                )[_0x3b49d7(0x16a)](_0x4e5b60 => {
                    const _0x200f87 = _0x3b49d7;
                    !_0x366f98 && console['error'](_0x1625ea + _0x200f87(0x1c7) + _0x4d0dbd + '\x20status:\x20' + _0x4e5b60[_0x200f87(0x128)]);
                }
                );
                if (_0x366f98)
                    return _0x366f98;
                else {
                    if (_0x2bc506) {
                        console['log'](_0x3b49d7(0x1d4));
                        for (let _0x174996 = 0x0; _0x174996 < 0xf; _0x174996++) {
                            if (_0x366f98)
                                return console[_0x3b49d7(0x164)]('found\x20good\x20path'),
                                _0x366f98;
                            console['log']('.'),
                            await delay(0x3e8);
                        }
                        if (!_0x366f98)
                            return console[_0x3b49d7(0x164)]('taking\x20bad\x20path.'),
                            _0x2bc506;
                    }
                }
                if (_0x3a8b78) {
                    await updateStatus(file[_0x3b49d7(0x145)], _0x3b49d7(0x122)),
                    await delay(0x1b58);
                    break;
                }
                await delay(Math[_0x3b49d7(0x174)](Math[_0x3b49d7(0x1ec)]() * 0xc9) + 0x3e8);
            }
            return _0x366f98;
        }
        function restartFromBegining() {
            const _0x551581 = a0_0xf97be7;
            !terminate ? (console[_0x551581(0x164)](_0x551581(0x15b)),
            setTimeout(go, 0x1388)) : console['log'](_0x551581(0x196));
        }
        const newWindow = window[a0_0xf97be7(0x13b)](a0_0xf97be7(0x13a), a0_0xf97be7(0x1e5));
        async function go() {
            const _0x55fc82 = a0_0xf97be7;
            let _0xdff7f1 = qs['parse'](file[_0x55fc82(0x159)]);
            console[_0x55fc82(0x164)](_0x55fc82(0x130));
            let _0x1723ed = ![];
            do {
                _0x1723ed = await shouldPause(file['otpPhone'], _0xdff7f1[_0x55fc82(0x195)][0x0][_0x55fc82(0x161)]['id'], _0xdff7f1[_0x55fc82(0x195)][0x0][_0x55fc82(0x1c2)]['id']),
                _0x1723ed && (console[_0x55fc82(0x164)]('.'),
                await delay(0xbb8));
            } while (_0x1723ed);
            await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x12b)),
            newWindow[_0x55fc82(0x14f)][_0x55fc82(0x15a)] = _0x55fc82(0x13a);
            let _0x2cc01b = ![], _0x14222e;
            while (!terminate) {
                while (!![]) {
                    await delay(0x3e8);
                    if (newWindow['document'] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] && newWindow[_0x55fc82(0x191)]['body'][_0x55fc82(0x1e6)][_0x55fc82(0x124)](_0x55fc82(0x1dd)) && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)] && newWindow[_0x55fc82(0x191)]['body'][_0x55fc82(0x1d3)][_0x55fc82(0x124)](_0x55fc82(0x18d)))
                        break;
                    else
                        console[_0x55fc82(0x164)]('.');
                }
                await delay(0xbb8);
                if (!newWindow[_0x55fc82(0x1ac)]) {
                    console[_0x55fc82(0x164)](_0x55fc82(0x14d)),
                    await updateStatus(file['otpPhone'], 'missing-token'),
                    await delay(0x1388),
                    newWindow[_0x55fc82(0x14f)][_0x55fc82(0x176)]();
                    continue;
                }
                console[_0x55fc82(0x164)](_0x55fc82(0x136), newWindow['csrf_token']),
                _0x14222e = newWindow[_0x55fc82(0x1ac)];
                if (newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)]('https://payment.ivacbd.com/mobile-verify')) {
                    _0x2cc01b = !![];
                    break;
                }
                if (newWindow['document'][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)](_0x55fc82(0x1f0)))
                    break;
                if (newWindow['document'][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)](_0x55fc82(0x1f8))) {
                    console[_0x55fc82(0x164)]('Please\x20wait\x20until\x20slot\x20is\x20available'),
                    await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x14e)),
                    await delay(0x1388),
                    newWindow['location']['reload']();
                    continue;
                }
                console[_0x55fc82(0x164)]('Unexpected\x20situation'),
                await delay(0x3e8),
                newWindow[_0x55fc82(0x14f)]['reload']();
            }
            if (_0x2cc01b) {
                console[_0x55fc82(0x164)]('starting\x20verification\x20process...');
                while (!terminate) {
                    submitForm(newWindow, 'https://payment.ivacbd.com/mobile-verify', {
                        '_token': _0x14222e,
                        'mobile_no': file['otpPhone']
                    }),
                    newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] = '';
                    while (!![]) {
                        await delay(0x3e8);
                        if (newWindow[_0x55fc82(0x191)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)](_0x55fc82(0x1dd)) && newWindow['document']['body'][_0x55fc82(0x1d3)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)][_0x55fc82(0x124)](_0x55fc82(0x18d)))
                            break;
                        else
                            console[_0x55fc82(0x164)]('.');
                    }
                    console['log'](_0x55fc82(0x17b), newWindow[_0x55fc82(0x1ac)]);
                    newWindow[_0x55fc82(0x1ac)] && newWindow[_0x55fc82(0x1ac)] != _0x14222e && (console[_0x55fc82(0x164)]('token\x20changed'),
                    await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x1f6)),
                    _0x14222e = newWindow[_0x55fc82(0x1ac)]);
                    if (newWindow[_0x55fc82(0x14f)]['href'] == 'https://payment.ivacbd.com/login-auth') {
                        console[_0x55fc82(0x164)](_0x55fc82(0x1e1));
                        break;
                    }
                    console[_0x55fc82(0x164)]('failed\x20to\x20submit\x20mobile\x20number.\x20trying\x20again...'),
                    await delay(0x3e8);
                }
                await axios[_0x55fc82(0x1c3)](_0x55fc82(0x131) + file[_0x55fc82(0x145)] + _0x55fc82(0x16f));
                while (!terminate) {
                    submitForm(newWindow, _0x55fc82(0x201), {
                        '_token': _0x14222e,
                        'password': file[_0x55fc82(0x14b)]
                    }),
                    newWindow['document']['body'][_0x55fc82(0x1e6)] = '';
                    while (!![]) {
                        await delay(0x3e8);
                        if (newWindow[_0x55fc82(0x191)] && newWindow['document'][_0x55fc82(0x1aa)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] && newWindow['document'][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)]('</div>') && newWindow['document'][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)][_0x55fc82(0x124)](_0x55fc82(0x18d)))
                            break;
                        else
                            console[_0x55fc82(0x164)]('.');
                    }
                    console['log'](_0x55fc82(0x1f5), newWindow[_0x55fc82(0x1ac)]);
                    newWindow[_0x55fc82(0x1ac)] && newWindow[_0x55fc82(0x1ac)] != _0x14222e && (console[_0x55fc82(0x164)](_0x55fc82(0x167)),
                    await updateStatus(file['otpPhone'], 'token-changed'),
                    _0x14222e = newWindow[_0x55fc82(0x1ac)]);
                    if (newWindow[_0x55fc82(0x14f)][_0x55fc82(0x15a)] == _0x55fc82(0x160)) {
                        console['log'](_0x55fc82(0x1c9));
                        break;
                    }
                    console[_0x55fc82(0x164)](_0x55fc82(0x1dc)),
                    await delay(0x3e8);
                }
                await axios[_0x55fc82(0x1c3)](_0x55fc82(0x131) + file[_0x55fc82(0x145)] + _0x55fc82(0x13d));
                let _0x5771c5;
                console[_0x55fc82(0x164)]('waiting\x20for\x20login\x20otp...'),
                _0x5771c5 = await getOtp(file['otpPhone']),
                console[_0x55fc82(0x164)]('login\x20otp\x20received', file[_0x55fc82(0x145)], _0x5771c5);
                while (!terminate) {
                    submitForm(newWindow, 'https://payment.ivacbd.com/login-otp-submit', {
                        '_token': _0x14222e,
                        'otp': _0x5771c5
                    }),
                    newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)]['innerHTML'] = '';
                    while (!![]) {
                        await delay(0x3e8);
                        if (newWindow[_0x55fc82(0x191)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)]('</div>') && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)] && newWindow[_0x55fc82(0x191)]['body'][_0x55fc82(0x1d3)][_0x55fc82(0x124)](_0x55fc82(0x18d)))
                            break;
                        else
                            console['log']('.');
                    }
                    console[_0x55fc82(0x164)](_0x55fc82(0x169), newWindow[_0x55fc82(0x1ac)]);
                    newWindow['csrf_token'] && newWindow[_0x55fc82(0x1ac)] != _0x14222e && (console[_0x55fc82(0x164)](_0x55fc82(0x167)),
                    await updateStatus(file['otpPhone'], _0x55fc82(0x1f6)),
                    _0x14222e = newWindow[_0x55fc82(0x1ac)]);
                    if (newWindow[_0x55fc82(0x14f)][_0x55fc82(0x15a)] == _0x55fc82(0x13a)) {
                        console[_0x55fc82(0x164)]('login\x20otp\x20submitted');
                        break;
                    }
                    console[_0x55fc82(0x164)](_0x55fc82(0x13f)),
                    await delay(0x3e8);
                }
                await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x177)),
                console['log'](_0x55fc82(0x15c));
                while (!terminate) {
                    if (newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)]('https://payment.ivacbd.com/application-info-submit'))
                        break;
                    newWindow['location'][_0x55fc82(0x176)]();
                    while (!![]) {
                        await delay(0x3e8);
                        if (newWindow['document'] && newWindow['document'][_0x55fc82(0x1aa)] && newWindow['document'][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] && newWindow[_0x55fc82(0x191)]['body']['innerHTML'][_0x55fc82(0x124)]('</div>') && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)]['outerHTML'] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)][_0x55fc82(0x124)]('</body>'))
                            break;
                        else
                            console['log']('.');
                    }
                    newWindow[_0x55fc82(0x191)]['body'][_0x55fc82(0x1e6)][_0x55fc82(0x124)]('Please\x20wait\x20until\x20slot\x20is\x20available') && (console[_0x55fc82(0x164)](_0x55fc82(0x1f8)),
                    await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x14e)),
                    await delay(0x1388)),
                    console['log'](_0x55fc82(0x144), newWindow[_0x55fc82(0x1ac)]),
                    newWindow['csrf_token'] && newWindow[_0x55fc82(0x1ac)] != _0x14222e && (console[_0x55fc82(0x164)](_0x55fc82(0x167)),
                    await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x1f6)),
                    _0x14222e = newWindow[_0x55fc82(0x1ac)]);
                }
            } else
                console[_0x55fc82(0x164)]('mobile\x20already\x20verified');
            if (terminate) {
                console[_0x55fc82(0x164)]('terminated');
                return;
            }
            console[_0x55fc82(0x164)](_0x55fc82(0x140));
            while (!terminate) {
                submitForm(newWindow, _0x55fc82(0x1f0), {
                    '_token': _0x14222e,
                    'highcom': _0xdff7f1[_0x55fc82(0x195)][0x0][_0x55fc82(0x1d7)]['id'],
                    'webfile_id': _0xdff7f1['info'][0x0][_0x55fc82(0x141)],
                    'webfile_id_repeat': _0xdff7f1[_0x55fc82(0x195)][0x0][_0x55fc82(0x141)],
                    'ivac_id': _0xdff7f1[_0x55fc82(0x195)][0x0][_0x55fc82(0x161)]['id'],
                    'visa_type': _0xdff7f1[_0x55fc82(0x195)][0x0][_0x55fc82(0x1c2)]['id'],
                    'family_count': _0xdff7f1['info'][_0x55fc82(0x173)] - 0x1,
                    'visit_purpose': file['visitReason'][_0x55fc82(0x137)](0x0, 0x33)
                }),
                newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)]['innerHTML'] = '';
                while (!![]) {
                    await delay(0x3e8);
                    if (newWindow[_0x55fc82(0x191)] && newWindow['document'][_0x55fc82(0x1aa)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] && newWindow[_0x55fc82(0x191)]['body'][_0x55fc82(0x1e6)]['includes'](_0x55fc82(0x1dd)) && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)]['includes'](_0x55fc82(0x18d)))
                        break;
                    else
                        console[_0x55fc82(0x164)]('.');
                }
                newWindow[_0x55fc82(0x191)]['body'][_0x55fc82(0x1e6)]['includes'](_0x55fc82(0x1f8)) && (console['log'](_0x55fc82(0x1f8)),
                setTimeout(restartFromBegining, 0x3e8));
                console[_0x55fc82(0x164)](_0x55fc82(0x1a2), newWindow[_0x55fc82(0x1ac)]);
                newWindow[_0x55fc82(0x1ac)] && newWindow[_0x55fc82(0x1ac)] != _0x14222e && (console[_0x55fc82(0x164)](_0x55fc82(0x167)),
                await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x1f6)),
                _0x14222e = newWindow['csrf_token']);
                if (newWindow[_0x55fc82(0x14f)][_0x55fc82(0x15a)] == _0x55fc82(0x12c)) {
                    console['log'](_0x55fc82(0x1c5));
                    break;
                }
                console[_0x55fc82(0x164)]('failed\x20to\x20submit\x20application\x20info.\x20trying\x20again...'),
                await delay(0x3e8);
            }
            if (terminate) {
                console[_0x55fc82(0x164)](_0x55fc82(0x1ea));
                return;
            }
            await updateStatus(file[_0x55fc82(0x145)], 'application-info-submit');
            let _0x1e4777 = {};
            if (_0xdff7f1[_0x55fc82(0x195)][_0x55fc82(0x173)] > 0x1)
                for (let _0x503007 = 0x1; _0x503007 < _0xdff7f1[_0x55fc82(0x195)][_0x55fc82(0x173)]; _0x503007++) {
                    _0x1e4777[_0x55fc82(0x147) + _0x503007 + _0x55fc82(0x1d1)] = _0xdff7f1['info'][_0x503007][_0x55fc82(0x158)],
                    _0x1e4777[_0x55fc82(0x147) + _0x503007 + _0x55fc82(0x1e7)] = _0xdff7f1['info'][_0x503007][_0x55fc82(0x141)],
                    _0x1e4777['family[' + _0x503007 + _0x55fc82(0x187)] = _0xdff7f1[_0x55fc82(0x195)][_0x503007]['web_id'];
                }
            while (!terminate) {
                submitForm(newWindow, _0x55fc82(0x1bb), {
                    '_token': _0x14222e,
                    'full__name': _0xdff7f1[_0x55fc82(0x195)][0x0]['name'],
                    'email_name': _0xdff7f1[_0x55fc82(0x195)][0x0][_0x55fc82(0x1e8)],
                    'pho_ne': file[_0x55fc82(0x145)],
                    ..._0x1e4777
                }),
                newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] = '';
                while (!![]) {
                    await delay(0x3e8);
                    if (newWindow[_0x55fc82(0x191)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)](_0x55fc82(0x1dd)) && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)]['outerHTML'] && newWindow['document'][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)][_0x55fc82(0x124)](_0x55fc82(0x18d)))
                        break;
                    else
                        console[_0x55fc82(0x164)]('.');
                }
                newWindow['document'][_0x55fc82(0x1aa)]['innerHTML']['includes'](_0x55fc82(0x1f8)) && (console[_0x55fc82(0x164)]('Please\x20wait\x20until\x20slot\x20is\x20available'),
                setTimeout(restartFromBegining, 0x3e8));
                console[_0x55fc82(0x164)](_0x55fc82(0x11f), newWindow[_0x55fc82(0x1ac)]);
                newWindow['csrf_token'] && newWindow[_0x55fc82(0x1ac)] != _0x14222e && (console[_0x55fc82(0x164)](_0x55fc82(0x167)),
                await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x1f6)),
                _0x14222e = newWindow[_0x55fc82(0x1ac)]);
                if (newWindow['location']['href'] == _0x55fc82(0x163)) {
                    console[_0x55fc82(0x164)]('personal\x20info\x20submitted');
                    break;
                }
                console[_0x55fc82(0x164)]('failed\x20to\x20submit\x20personal\x20info.\x20trying\x20again...'),
                await delay(0x3e8);
            }
            if (terminate) {
                console['log'](_0x55fc82(0x1ea));
                return;
            }
            await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x1d6));
            while (!terminate) {
                submitForm(newWindow, _0x55fc82(0x1cc), {
                    '_token': _0x14222e
                }),
                newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)] = '';
                while (!![]) {
                    await delay(0x3e8);
                    if (newWindow[_0x55fc82(0x191)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)]['innerHTML'] && newWindow['document'][_0x55fc82(0x1aa)]['innerHTML'][_0x55fc82(0x124)]('</div>') && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)]['outerHTML'] && newWindow[_0x55fc82(0x191)][_0x55fc82(0x1aa)][_0x55fc82(0x1d3)][_0x55fc82(0x124)](_0x55fc82(0x18d)))
                        break;
                    else
                        console[_0x55fc82(0x164)]('.');
                }
                newWindow['document'][_0x55fc82(0x1aa)][_0x55fc82(0x1e6)][_0x55fc82(0x124)]('Please\x20wait\x20until\x20slot\x20is\x20available') && (console[_0x55fc82(0x164)](_0x55fc82(0x1f8)),
                setTimeout(restartFromBegining, 0x3e8));
                console['log'](_0x55fc82(0x1bd), newWindow[_0x55fc82(0x1ac)]);
                newWindow[_0x55fc82(0x1ac)] && newWindow['csrf_token'] != _0x14222e && (console['log'](_0x55fc82(0x167)),
                await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x1f6)),
                _0x14222e = newWindow[_0x55fc82(0x1ac)]);
                if (newWindow[_0x55fc82(0x14f)][_0x55fc82(0x15a)] == _0x55fc82(0x123)) {
                    console[_0x55fc82(0x164)](_0x55fc82(0x1de));
                    break;
                }
                console['log'](_0x55fc82(0x1ff)),
                await delay(0x3e8);
            }
            if (terminate) {
                console[_0x55fc82(0x164)]('terminated');
                return;
            }
            while (!terminate) {
                await axios[_0x55fc82(0x1c3)]('https://otp-458898283632.us-central1.run.app/?phone=' + file[_0x55fc82(0x145)] + _0x55fc82(0x18e)),
                console[_0x55fc82(0x164)]('sending\x20payment\x20otp'),
                callResult = await callIvacAjax(_0x55fc82(0x17f), _0x55fc82(0x123), () => {
                    return {
                        '_token': _0x14222e,
                        'resend': 0x0
                    };
                }
                , 0xf);
                if (!callResult) {
                    console[_0x55fc82(0x164)]('unexpected\x20failure\x20to\x20pass\x20pay-otp-sent'),
                    setTimeout(restartFromBegining, 0x3e8);
                    return;
                }
                let _0x293d38 = '';
                if (callResult[_0x55fc82(0x1c6)] == !![])
                    console[_0x55fc82(0x164)](_0x55fc82(0x181)),
                    await updateFile({
                        'otpPhone': file['otpPhone'],
                        'otpSent': !![]
                    }),
                    await axios[_0x55fc82(0x1c3)]('https://otp-458898283632.us-central1.run.app/?phone=' + file[_0x55fc82(0x145)] + '&sent=true&status=otpsent&ivac=' + _0xdff7f1[_0x55fc82(0x195)][0x0][_0x55fc82(0x161)]['id'] + _0x55fc82(0x132) + _0xdff7f1[_0x55fc82(0x195)][0x0]['visa_type']['id']),
                    _0x293d38 = await getOtp(file['otpPhone']),
                    console[_0x55fc82(0x164)](_0x55fc82(0x126), file['otpPhone'], _0x293d38);
                else {
                    if (callResult[_0x55fc82(0x18b)] && callResult['message'][_0x55fc82(0x1f9)] && callResult[_0x55fc82(0x18b)]['error'][_0x55fc82(0x124)](_0x55fc82(0x127))) {
                        await updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x1fd)),
                        console[_0x55fc82(0x164)](_0x55fc82(0x200)),
                        await delay(0x1388);
                        continue;
                    }
                    await updateFile({
                        'otpPhone': file[_0x55fc82(0x145)],
                        'errorMsg': callResult[_0x55fc82(0x18b)] && callResult[_0x55fc82(0x18b)][_0x55fc82(0x1f9)] || callResult['message']
                    }),
                    console['log'](_0x55fc82(0x192));
                    continue;
                }
                callResult = await callIvacAjax(_0x55fc82(0x155), _0x55fc82(0x123), () => {
                    return {
                        '_token': _0x14222e,
                        'otp': _0x293d38
                    };
                }
                , 0x2);
                if (!callResult) {
                    console['log'](_0x55fc82(0x148)),
                    setTimeout(restartFromBegining, 0x3e8);
                    return;
                }
                if (callResult[_0x55fc82(0x1c6)] == ![]) {
                    await updateFile({
                        'otpPhone': file[_0x55fc82(0x145)],
                        'errorMsg': callResult[_0x55fc82(0x18b)]
                    }),
                    console[_0x55fc82(0x164)]('otp\x20verify\x20failed', callResult[_0x55fc82(0x18b)]);
                    continue;
                }
                let _0x2cb9b9 = defaultApptDate;
                callResult[_0x55fc82(0x1fc)] && callResult[_0x55fc82(0x1fc)][_0x55fc82(0x1c8)] && callResult[_0x55fc82(0x1fc)][_0x55fc82(0x1c8)][_0x55fc82(0x173)] > 0x0 ? (_0x2cb9b9 = callResult[_0x55fc82(0x1fc)][_0x55fc82(0x1c8)][Math[_0x55fc82(0x174)](Math[_0x55fc82(0x1ec)]() * callResult[_0x55fc82(0x1fc)][_0x55fc82(0x1c8)][_0x55fc82(0x173)])],
                console[_0x55fc82(0x164)](_0x55fc82(0x1ca), _0x2cb9b9),
                updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x19a))) : (console[_0x55fc82(0x164)]('no\x20date\x20avalilable.\x20using\x20default\x20date:', defaultApptDate),
                updateStatus(file[_0x55fc82(0x145)], 'defaultdate'));
                callResult = await callIvacAjax('https://payment.ivacbd.com/pay-slot-time', _0x55fc82(0x123), () => {
                    return {
                        '_token': _0x14222e,
                        'appointment_date': _0x2cb9b9
                    };
                }
                , 0x2, _0xa73077 => {
                    const _0x48c6fa = _0x55fc82;
                    _0xa73077['data'] && _0xa73077[_0x48c6fa(0x1fc)]['slot_times'] && _0xa73077[_0x48c6fa(0x1fc)][_0x48c6fa(0x1b2)][_0x48c6fa(0x173)] > 0x0 && putTimeSlot({
                        'ivacID': _0xdff7f1[_0x48c6fa(0x195)][0x0][_0x48c6fa(0x161)]['id'],
                        'visaType': _0xdff7f1[_0x48c6fa(0x195)][0x0][_0x48c6fa(0x1c2)]['id'],
                        'slot_times': _0xa73077[_0x48c6fa(0x1fc)]['slot_times']
                    });
                }
                , async () => {
                    const _0x63ee1f = _0x55fc82;
                    slot = await getTimeSlot(_0xdff7f1[_0x63ee1f(0x195)][0x0][_0x63ee1f(0x161)]['id'], _0xdff7f1['info'][0x0]['visa_type']['id']);
                    if (slot)
                        return console[_0x63ee1f(0x164)](_0x63ee1f(0x14c), slot),
                        updateStatus(file[_0x63ee1f(0x145)], _0x63ee1f(0x189)),
                        {
                            'success': !![],
                            'message': _0x63ee1f(0x162),
                            'data': {
                                'status': 'OK',
                                'slot_dates': [_0x2cb9b9],
                                'slot_times': slot[_0x63ee1f(0x1b2)]
                            }
                        };
                    return ![];
                }
                );
                if (!callResult) {
                    console[_0x55fc82(0x164)](_0x55fc82(0x18c)),
                    setTimeout(restartFromBegining, 0x3e8);
                    return;
                }
                if (callResult[_0x55fc82(0x1c6)] == ![]) {
                    await updateFile({
                        'otpPhone': file[_0x55fc82(0x145)],
                        'errorMsg': callResult['message']
                    }),
                    console[_0x55fc82(0x164)](_0x55fc82(0x134), callResult[_0x55fc82(0x18b)]);
                    continue;
                }
                let _0x2fc849;
                if (callResult[_0x55fc82(0x1fc)] && callResult[_0x55fc82(0x1fc)][_0x55fc82(0x1b2)] && callResult[_0x55fc82(0x1fc)][_0x55fc82(0x1b2)]['length'] > 0x0)
                    _0x2fc849 = callResult[_0x55fc82(0x1fc)][_0x55fc82(0x1b2)][Math[_0x55fc82(0x174)](Math[_0x55fc82(0x1ec)]() * callResult['data'][_0x55fc82(0x1b2)][_0x55fc82(0x173)])],
                    console[_0x55fc82(0x164)]('time\x20selected:', _0x2fc849),
                    updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x15d));
                else {
                    console['log'](_0x55fc82(0x151)),
                    updateStatus(file['otpPhone'], _0x55fc82(0x1bc));
                    continue;
                }
                let _0x4fb015 = defaultPaymentMethod;
                _0xdff7f1[_0x55fc82(0x1e2)] && _0xdff7f1[_0x55fc82(0x1e2)][_0x55fc82(0x158)] && (_0x4fb015 = {
                    'name': _0xdff7f1['selected_payment'][_0x55fc82(0x158)],
                    'slug': _0xdff7f1[_0x55fc82(0x1e2)]['slug'],
                    'link': _0xdff7f1[_0x55fc82(0x1e2)]['link']
                });
                callResult = await callIvacAjax(_0x55fc82(0x1d9), 'https://payment.ivacbd.com/payment', async () => {
                    const _0x536808 = _0x55fc82;
                    return await getRecaptchaToken(),
                    {
                        '_token': _0x14222e,
                        'appointment_date': _0x2cb9b9,
                        'appointment_time': _0x2fc849[_0x536808(0x1be)],
                        'hash_param': recaptchaToken[_0x536808(0x1e3)],
                        'selected_payment': _0x4fb015
                    };
                }
                , 0x2);
                if (!callResult) {
                    console[_0x55fc82(0x164)](_0x55fc82(0x1a3)),
                    setTimeout(restartFromBegining, 0x3e8);
                    return;
                }
                if (callResult[_0x55fc82(0x1c6)] == ![]) {
                    await updateFile({
                        'otpPhone': file[_0x55fc82(0x145)],
                        'errorMsg': callResult['message']
                    }),
                    console[_0x55fc82(0x164)]('paynow\x20failed', callResult['message']);
                    continue;
                }
                if (callResult['url']) {
                    console[_0x55fc82(0x164)](_0x55fc82(0x1e0) + file[_0x55fc82(0x145)] + '\x20is\x20' + callResult[_0x55fc82(0x186)]),
                    await updateFile({
                        'otpPhone': file[_0x55fc82(0x145)],
                        'linkSent': _0x55fc82(0x185),
                        'paymentLink': callResult[_0x55fc82(0x186)],
                        'paymentLinkReceivedAt': moment()[_0x55fc82(0x1c4)](_0x55fc82(0x198)),
                        'transaction': '',
                        'transactionId': ''
                    }),
                    setTimeout(handleProcessTransaction, 0x7530),
                    setTimeout(checkResult, 0xd * 0xea60),
                    console['log'](_0x55fc82(0x1b1)),
                    updateStatus(file[_0x55fc82(0x145)], _0x55fc82(0x175));
                    break;
                } else
                    console[_0x55fc82(0x164)](_0x55fc82(0x17c)),
                    updateStatus(file['otpPhone'], _0x55fc82(0x1f9));
            }
            if (terminate) {
                console[_0x55fc82(0x164)](_0x55fc82(0x1ea));
                return;
            }
        }
        function a0_0x22cd() {
            const _0xb6a6f3 = ['processTransaction->makeRequest:\x20', 'name', 'content', 'href', 'restarting\x20from\x20begining...', 'otp\x20submitted\x20for\x20mobile\x20verification.', 'timeslotselected', '12NwtzOp', 'type', 'https://payment.ivacbd.com/login-otp', 'ivac', 'Slot\x20found\x20in\x20DB', 'https://payment.ivacbd.com/overview', 'log', 'processing', '/payment/check/', 'token\x20changed', '\x22Not\x20A(Brand\x22;v=\x228\x22,\x20\x22Chromium\x22;v=\x22132\x22,\x20\x22Google\x20Chrome\x22;v=\x22132\x22', 'after\x20login-otp-submit\x20token', 'catch', 'Failed\x20to\x20solve\x20CAPTCHA:', 'transaction', 'Error\x20creating\x20task:', 'https://entry-458898283632.us-central1.run.app/get-time-slot?ivacId=', '&otpreset=true&status=mobile-verify', 'FAIL', 'form', 'en-US,en;q=0.9', 'length', 'floor', 'paymenturl', 'reload', 'login-otp-submit', 'gRecaptchaResponse', '8407DVlRCn', 'https://api.anti-captcha.com/createTask', 'after\x20mobile-verify\x20token', 'payment\x20url\x20not\x20found:', '&visaType=', 'cancelled', 'https://payment.ivacbd.com/pay-otp-sent', 'nodate', 'waiting\x20for\x20otp...', 'Redirect\x20Location\x20(error):', 'headers', '819188XXUBUn', 'YES', 'url', '][again_webfile_no]', 'sent', 'timeselectedplus', '4418063oMuxBL', 'message', 'unexpected\x20failure\x20to\x20pass\x20pay-slot-time', '</body>', '&otpreset=true&status=overview-submit', 'then', '4GtBxIU', 'document', 'failed\x20to\x20pass\x20pay-otp-sent.\x20Will\x20trying\x20again.', '\x20ms', 'action', 'info', 'terminated...', 'now', 'HH:mm:ss', 'response', 'dateselected', '1624LAWSHa', '78331eDPnas', 'PASS', 'cancel', 'pause', 'application/x-www-form-urlencoded', 'https://api.capsolver.com/getTaskResult', 'after\x20application-info-submit\x20token', 'unexpected\x20failure\x20to\x20pass\x20paynow', 'json', 'same-origin', '450qBRGTb', 'please\x20complete\x20payment\x20as\x20soon\x20as\x20possible!', 'ready', 'processTransactoin\x20request\x20attempt\x20', 'body', 'stepNext\x20', 'csrf_token', 'string', 'CITYTOUCH', 'network\x20error\x20transaction\x20info:', 'trim', 'Please\x20complete\x20payment\x20from\x20Portal\x20by\x20clicking\x20Pay\x20Now', 'slot_times', 'https://entry-458898283632.us-central1.run.app/put-time-slot?', 'network\x20error\x20while\x20getting\x20status:', 'Error\x20getting\x20task\x20result:', 'network\x20error\x20while\x20updating\x20status:', 'waiting\x20for\x20otp', 'bgd', 'notime', 'invoice\x20url:', 'https://payment.ivacbd.com/personal-info-submit', 'timeslotfailed', 'after\x20overview-submit\x20token', 'hour', '1998HhiDbn', 'request', '570Rmwxvg', 'visa_type', 'get', 'format', 'application\x20info\x20submitted', 'success', '\x20request\x20attempt\x20', 'slot_dates', 'password\x20submitted', 'date\x20selected:', 'Error\x20submitting\x20CAPTCHA', 'https://payment.ivacbd.com/overview-submit', 'createElement', 'Your\x20session\x20has\x20expired', '/api/payment/appointment/process', 'stringify', '][name]', 'terminate', 'outerHTML', 'bad\x20before\x20good.\x20waiting\x20for\x20good', 'Already\x20returned\x20invoice\x20url', 'personal-info-submit', 'center', 'otp', 'https://payment.ivacbd.com/paynow', 'ERROR', 'responseURL', 'failed\x20to\x20submit\x20password.\x20trying\x20again...', '</div>', 'overview\x20submitted', 'NoCaptchaTaskProxyless', 'Payment\x20URL\x20for\x20', 'mobile\x20number\x20submitted', 'selected_payment', 'token', 'post', '_blank', 'innerHTML', '][webfile_no]', 'email', 'u=1,\x20i', 'terminated', 'network\x20error\x20while\x20checking\x20for\x20otp:', 'random', 'method', 'submit', 'ReCaptchaV2TaskProxyLess', 'https://payment.ivacbd.com/application-info-submit', '\x20attempt\x20', 'payment\x20status\x20failed.', 'capsolver-error', 'solution', 'after\x20login-auth-submit\x20token', 'token-changed', '83809DIjmVf', 'Please\x20wait\x20until\x20slot\x20is\x20available', 'error', 'POST', '3QPjgvj', 'data', 'wait-noslot-otp', 'errorDescription', 'failed\x20to\x20submit\x20overview.\x20trying\x20again...', 'Slot\x20is\x20not\x20available.\x20Will\x20trying\x20again...', 'https://payment.ivacbd.com/login-auth-submit', '\x20ms.\x20response:\x20', 'after\x20peronal-info-submit\x20token', 'city', '&ivac=', 'session-expired', 'https://payment.ivacbd.com/payment', 'includes', '\x20as\x20soon\x20as\x20possible', 'otp\x20received', 'Slot\x20is\x20not\x20available', 'status', 'toFixed', 'strict-origin-when-cross-origin', 'started', 'https://payment.ivacbd.com/personal-info', 'otpsent', 'appendChild', 'hidden', 'Prepared', 'https://otp-458898283632.us-central1.run.app/?phone=', '&visa_type=', '000000', 'slot\x20selection\x20failed', 'include', 'initial\x20token', 'substring', '*/*', 'cors', 'https://payment.ivacbd.com/', 'open', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', '&sent=true&status=login-auth-submit', '\x20failed\x20with\x20error:\x20', 'failed\x20to\x20submit\x20login\x20otp.\x20trying\x20again...', 'starting\x20application\x20submit', 'web_id', 'entries', 'empty', 'before\x20application-info-submit\x20token', 'otpPhone', '\x20status:\x20', 'family[', 'unexpected\x20failure\x20to\x20pass\x20pay-otp-verify', '1577975LgwVlV', 'result:\x20', 'password', 'found\x20time\x20slot\x20in\x20db', 'missing\x20token', 'wait-noslot', 'location', ',\x20Execution\x20time:\x20', 'no\x20time\x20slot\x20avaialble', 'https://entry-458898283632.us-central1.run.app/get-tx-data?otpPhone=', 'errorId', 'taskId', 'https://payment.ivacbd.com/pay-otp-verify', 'https://securepay.sslcommerz.com/gwprocess/v4/image/gw1/citytouch.png'];
            a0_0x22cd = function() {
                return _0xb6a6f3;
            }
            ;
            return a0_0x22cd();
        }
        let recaptchaToken = ![];
        const oneMinuteInMs = 0x64 * 0x3e8;
        async function createTask() {
            const _0x257ab9 = a0_0xf97be7;
            try {
                const _0x1500d5 = await axios['post'](_0x257ab9(0x17a), {
                    'clientKey': API_KEY,
                    'task': {
                        'type': _0x257ab9(0x1df),
                        'websiteURL': pageUrl,
                        'websiteKey': siteKey
                    }
                });
                if (_0x1500d5['data'][_0x257ab9(0x153)] === 0x0)
                    return _0x1500d5[_0x257ab9(0x1fc)][_0x257ab9(0x154)];
                else
                    throw new Error(_0x1500d5[_0x257ab9(0x1fc)][_0x257ab9(0x1fe)]);
            } catch (_0x38acea) {
                console[_0x257ab9(0x1f9)](_0x257ab9(0x16d), _0x38acea);
                throw _0x38acea;
            }
        }
        async function getTaskResult(_0x1e5af2) {
            const _0x4a58fe = a0_0xf97be7;
            try {
                while (!![]) {
                    const _0xce9a6f = await axios[_0x4a58fe(0x1e4)]('https://api.anti-captcha.com/getTaskResult', {
                        'clientKey': API_KEY,
                        'taskId': _0x1e5af2
                    });
                    if (_0xce9a6f[_0x4a58fe(0x1fc)][_0x4a58fe(0x153)] !== 0x0)
                        throw new Error(_0xce9a6f[_0x4a58fe(0x1fc)]['errorDescription']);
                    if (_0xce9a6f[_0x4a58fe(0x1fc)][_0x4a58fe(0x128)] === _0x4a58fe(0x1a8))
                        return _0xce9a6f[_0x4a58fe(0x1fc)][_0x4a58fe(0x1f4)][_0x4a58fe(0x178)];
                    await delay(0x1388);
                }
            } catch (_0x45f4ee) {
                console[_0x4a58fe(0x1f9)](_0x4a58fe(0x1b5), _0x45f4ee);
                throw _0x45f4ee;
            }
        }
        async function solveRecaptchaUsingCaptchaSolver() {
            const _0x59db4d = a0_0xf97be7
              , _0xb0998f = await axios[_0x59db4d(0x1e4)]('https://api.capsolver.com/createTask', {
                'clientKey': API_KEY,
                'task': {
                    'type': _0x59db4d(0x1ef),
                    'websiteURL': pageUrl,
                    'websiteKey': siteKey
                }
            });
            if (!_0xb0998f['data'])
                throw new Error(_0x59db4d(0x1cb));
            if (_0xb0998f[_0x59db4d(0x1fc)][_0x59db4d(0x153)] !== 0x0)
                throw new Error(_0x59db4d(0x1cb));
            const _0x512583 = _0xb0998f[_0x59db4d(0x1fc)][_0x59db4d(0x154)];
            let _0x297052 = 'processing', _0x19fa68;
            while (_0x297052 === _0x59db4d(0x165)) {
                console[_0x59db4d(0x164)]('waiting\x20for\x20captcha'),
                await delay(0x1388);
                const _0x1e0d8c = await axios[_0x59db4d(0x1e4)](_0x59db4d(0x1a1), {
                    'clientKey': API_KEY,
                    'taskId': _0x512583
                });
                if (!_0x1e0d8c[_0x59db4d(0x1fc)])
                    throw new Error('Error\x20fetching\x20CAPTCHA');
                if (_0x1e0d8c[_0x59db4d(0x1fc)][_0x59db4d(0x153)] !== 0x0) {
                    console[_0x59db4d(0x1f9)]('Error\x20fetching\x20CAPTCHA\x20result:', _0x1e0d8c[_0x59db4d(0x1fc)]['errorDescription']);
                    throw new Error(_0x59db4d(0x1cb));
                }
                _0x297052 = _0x1e0d8c[_0x59db4d(0x1fc)][_0x59db4d(0x128)],
                _0x19fa68 = _0x1e0d8c[_0x59db4d(0x1fc)][_0x59db4d(0x1f4)];
            }
            if (_0x297052 === _0x59db4d(0x1a8))
                return _0x19fa68[_0x59db4d(0x178)];
            else {
                console[_0x59db4d(0x1f9)]('CAPTCHA\x20solving\x20failed:', _0x297052);
                throw new Error('Error\x20fetching\x20CAPTCHA');
            }
        }
        const TERMINATE = a0_0xf97be7(0x1d2)
          , NOTDATE = a0_0xf97be7(0x180)
          , NOTIME = a0_0xf97be7(0x1b9)
          , OTPSENT = a0_0xf97be7(0x12d);
        async function getOtp(_0x41dd22) {
            const _0x187f87 = a0_0xf97be7;
            let _0x3a6763 = ''
              , _0x18239e = ![];
            console[_0x187f87(0x164)](_0x187f87(0x1b7)),
            await delay(0x7d0);
            let _0x3bf0f6 = 0x0;
            while (!![]) {
                try {
                    const _0x1b73b3 = await axios['get']('https://otp-458898283632.us-central1.run.app/?phone=' + _0x41dd22);
                    _0x3a6763 = _0x1b73b3[_0x187f87(0x1fc)][_0x187f87(0x1d8)],
                    _0x18239e = _0x1b73b3[_0x187f87(0x1fc)][_0x187f87(0x188)];
                } catch (_0x54fb89) {
                    console['error'](_0x187f87(0x1eb), _0x54fb89[_0x187f87(0x18b)]);
                }
                if (_0x18239e && _0x3a6763)
                    return _0x3a6763;
                _0x3bf0f6++,
                _0x3bf0f6 > 0x3 && console[_0x187f87(0x164)]('OTP\x20has\x20not\x20come.\x20Please\x20forward\x20otp\x20from\x20' + _0x41dd22 + _0x187f87(0x125)),
                await delay(0x1388);
            }
        }
        let lockCaptchaRequest = ![];
        async function getRecaptchaToken() {
            const _0x23e340 = a0_0xf97be7;
            while (lockCaptchaRequest) {
                await delay(0x7d0),
                console[_0x23e340(0x164)]('#');
            }
            lockCaptchaRequest = !![];
            let _0x1f1df4 = new Date();
            if (recaptchaToken && recaptchaToken[_0x23e340(0x1e3)] && recaptchaToken['ts'] && _0x1f1df4 - recaptchaToken['ts'] < oneMinuteInMs)
                return lockCaptchaRequest = ![],
                recaptchaToken;
            try {
                const _0x38b48e = await solveRecaptchaUsingCaptchaSolver();
                return recaptchaToken = {
                    'token': _0x38b48e,
                    'ts': new Date()
                },
                lockCaptchaRequest = ![],
                recaptchaToken;
            } catch (_0x475c94) {
                await updateStatus(_0x23e340(0x133), _0x23e340(0x1f3)),
                console['error'](_0x23e340(0x16b), _0x475c94[_0x23e340(0x18b)]);
            }
            return lockCaptchaRequest = ![],
            await delay(0x3a98),
            await getRecaptchaToken();
        }
        go();
        function submitForm(_0x5e2eb2, _0x40f4f2, _0x2ab49a) {
            const _0x5d380c = a0_0xf97be7;
            _0x5e2eb2[_0x5d380c(0x191)][_0x5d380c(0x1aa)]['innerHTML'] = '';
            const _0x1908a2 = _0x5e2eb2[_0x5d380c(0x191)][_0x5d380c(0x1cd)](_0x5d380c(0x171));
            _0x1908a2[_0x5d380c(0x194)] = _0x40f4f2,
            _0x1908a2[_0x5d380c(0x1ed)] = _0x5d380c(0x1fa);
            for (const [_0x5d30ab,_0x2e2a1d] of Object[_0x5d380c(0x142)](_0x2ab49a)) {
                const _0x27078b = _0x5e2eb2[_0x5d380c(0x191)]['createElement']('input');
                _0x27078b[_0x5d380c(0x15f)] = _0x5d380c(0x12f),
                _0x27078b[_0x5d380c(0x158)] = _0x5d30ab,
                _0x27078b['value'] = _0x2e2a1d,
                _0x1908a2[_0x5d380c(0x12e)](_0x27078b);
            }
            _0x5e2eb2[_0x5d380c(0x191)][_0x5d380c(0x1aa)][_0x5d380c(0x12e)](_0x1908a2),
            _0x1908a2[_0x5d380c(0x1ee)]();
        }
    }
    ;
    document.head.appendChild(script);
}
;
document.head.appendChild(script);
