<?php
/**
 * IVAC Dashboard Authentication API
 */

if (!defined('ABSPATH')) {
    exit;
}

class IVAC_Auth_API {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->register_routes();
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Authentication endpoint
        register_rest_route('ivac/v1', '/auth', array(
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => array($this, 'authenticate'),
            'permission_callback' => '__return_true', // Public endpoint
            'args' => array(
                'apiKey' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));
        
        // Generate API key endpoint (for logged-in users)
        register_rest_route('ivac/v1', '/auth/generate-key', array(
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => array($this, 'generate_api_key'),
            'permission_callback' => array($this, 'check_admin_permissions'),
            'args' => array(
                'name' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));
        
        // List API keys endpoint
        register_rest_route('ivac/v1', '/auth/keys', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_api_keys'),
            'permission_callback' => array($this, 'check_admin_permissions'),
        ));
        
        // Revoke API key endpoint
        register_rest_route('ivac/v1', '/auth/keys/(?P<id>\d+)', array(
            'methods' => WP_REST_Server::DELETABLE,
            'callback' => array($this, 'revoke_api_key'),
            'permission_callback' => array($this, 'check_admin_permissions'),
        ));
    }
    
    /**
     * Check admin permissions
     */
    public function check_admin_permissions() {
        return current_user_can('manage_options');
    }
    
    /**
     * Authenticate API key
     */
    public function authenticate($request) {
        $api_key = $request->get_param('apiKey');
        
        if (empty($api_key)) {
            return new WP_Error('missing_api_key', 'API key is required', array('status' => 400));
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_api_keys';
        
        // Check if API key exists and is active
        $key_data = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE api_key = %s AND is_active = 1",
            $api_key
        ), ARRAY_A);
        
        if (!$key_data) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => 'Invalid API Key'
            ), 401);
        }
        
        // Update last used timestamp
        $wpdb->update(
            $table_name,
            array('last_used' => current_time('mysql')),
            array('id' => $key_data['id'])
        );
        
        // Get user data
        $user = get_user_by('id', $key_data['user_id']);
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Authentication successful',
            'user' => array(
                'id' => $user->ID,
                'name' => $user->display_name,
                'email' => $user->user_email
            ),
            'key_info' => array(
                'name' => $key_data['name'],
                'created_at' => $key_data['created_at'],
                'last_used' => $key_data['last_used']
            )
        ), 200);
    }
    
    /**
     * Generate new API key
     */
    public function generate_api_key($request) {
        $name = $request->get_param('name');
        $user_id = get_current_user_id();
        
        if (empty($name)) {
            return new WP_Error('missing_name', 'API key name is required', array('status' => 400));
        }
        
        // Generate unique API key
        $api_key = $this->generate_unique_api_key();
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_api_keys';
        
        // Insert new API key
        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'api_key' => $api_key,
                'name' => $name,
                'permissions' => json_encode(array('read', 'write')),
                'created_at' => current_time('mysql'),
                'is_active' => 1
            )
        );
        
        if ($result === false) {
            return new WP_Error('db_error', 'Failed to generate API key', array('status' => 500));
        }
        
        $key_id = $wpdb->insert_id;
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'API key generated successfully',
            'api_key' => $api_key,
            'key_id' => $key_id,
            'name' => $name,
            'created_at' => current_time('mysql')
        ), 201);
    }
    
    /**
     * Get all API keys for current user
     */
    public function get_api_keys($request) {
        $user_id = get_current_user_id();
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_api_keys';
        
        $keys = $wpdb->get_results($wpdb->prepare(
            "SELECT id, name, api_key, created_at, last_used, is_active FROM $table_name WHERE user_id = %d ORDER BY created_at DESC",
            $user_id
        ), ARRAY_A);
        
        // Mask API keys for security (show only first 8 and last 4 characters)
        foreach ($keys as &$key) {
            $api_key = $key['api_key'];
            $key['api_key_masked'] = substr($api_key, 0, 8) . '...' . substr($api_key, -4);
            unset($key['api_key']); // Remove full API key from response
        }
        
        return new WP_REST_Response(array(
            'keys' => $keys
        ), 200);
    }
    
    /**
     * Revoke API key
     */
    public function revoke_api_key($request) {
        $key_id = $request->get_param('id');
        $user_id = get_current_user_id();
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_api_keys';
        
        // Check if key belongs to current user
        $key_data = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
            $key_id,
            $user_id
        ), ARRAY_A);
        
        if (!$key_data) {
            return new WP_Error('key_not_found', 'API key not found', array('status' => 404));
        }
        
        // Deactivate the key instead of deleting it
        $result = $wpdb->update(
            $table_name,
            array('is_active' => 0),
            array('id' => $key_id)
        );
        
        if ($result === false) {
            return new WP_Error('db_error', 'Failed to revoke API key', array('status' => 500));
        }
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'API key revoked successfully'
        ), 200);
    }
    
    /**
     * Generate unique API key
     */
    private function generate_unique_api_key() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_api_keys';
        
        do {
            // Generate 64-character random string
            $api_key = bin2hex(random_bytes(32));
            
            // Check if key already exists
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE api_key = %s",
                $api_key
            ));
        } while ($exists > 0);
        
        return $api_key;
    }
    
    /**
     * Validate API key for protected endpoints
     */
    public static function validate_api_key($request) {
        $auth_header = $request->get_header('authorization');
        
        if (!$auth_header) {
            return new WP_Error('missing_auth', 'Authorization header is required', array('status' => 401));
        }
        
        // Extract API key from Bearer token
        if (strpos($auth_header, 'Bearer ') !== 0) {
            return new WP_Error('invalid_auth_format', 'Authorization must be Bearer token', array('status' => 401));
        }
        
        $api_key = substr($auth_header, 7);
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'ivac_api_keys';
        
        // Check if API key exists and is active
        $key_data = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE api_key = %s AND is_active = 1",
            $api_key
        ), ARRAY_A);
        
        if (!$key_data) {
            return new WP_Error('invalid_api_key', 'Invalid or inactive API key', array('status' => 401));
        }
        
        // Update last used timestamp
        $wpdb->update(
            $table_name,
            array('last_used' => current_time('mysql')),
            array('id' => $key_data['id'])
        );
        
        return true;
    }
}
