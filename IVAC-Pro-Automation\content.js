/**
 * IVAC Pro Automation - Content Script
 * Handles direct interaction with the IVAC website
 */

class IVACContentScript {
    constructor() {
        this.isInitialized = false;
        this.currentAction = null;
        this.actionTimeout = null;
        
        this.init();
    }

    /**
     * Initialize content script
     */
    init() {
        if (this.isInitialized) return;
        
        console.log('IVAC Pro Automation: Content script initialized');
        this.isInitialized = true;
        
        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Notify background script that content script is ready
        chrome.runtime.sendMessage({
            action: 'contentScriptReady',
            url: window.location.href
        });
    }

    /**
     * Handle messages from background script
     */
    async handleMessage(message, sendResponse) {
        try {
            console.log('Content script received message:', message);
            
            switch (message.action) {
                case 'fillLoginForm':
                    await this.fillLoginForm(message.data);
                    sendResponse({ success: true });
                    break;
                    
                case 'fillApplicationForm':
                    await this.fillApplicationForm(message.data);
                    sendResponse({ success: true });
                    break;
                    
                case 'selectDate':
                    await this.selectDate(message.data.date);
                    sendResponse({ success: true });
                    break;
                    
                case 'getRecaptchaDetails':
                    const recaptchaDetails = await this.getRecaptchaDetails();
                    sendResponse({ success: true, data: recaptchaDetails });
                    break;
                    
                case 'submitRecaptchaToken':
                    await this.submitRecaptchaToken(message.data.token);
                    sendResponse({ success: true });
                    break;
                    
                case 'getPageInfo':
                    const pageInfo = await this.getPageInfo();
                    sendResponse({ success: true, data: pageInfo });
                    break;
                    
                case 'clickElement':
                    await this.clickElement(message.data.selector);
                    sendResponse({ success: true });
                    break;
                    
                case 'waitForElement':
                    await this.waitForElement(message.data.selector, message.data.timeout);
                    sendResponse({ success: true });
                    break;
                    
                case 'extractData':
                    const extractedData = await this.extractData(message.data.selectors);
                    sendResponse({ success: true, data: extractedData });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action: ' + message.action });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * Fill login form with credentials
     */
    async fillLoginForm(credentials) {
        console.log('Filling login form...');
        
        // Wait for form to be available
        await this.waitForElement('form', 5000);
        
        // Fill mobile number
        const mobileInput = document.querySelector('input[name="mobile_no"], input[type="tel"], #mobile_no');
        if (mobileInput) {
            this.fillInput(mobileInput, credentials.mobileNumber);
            await this.delay(500);
        }
        
        // Fill password
        const passwordInput = document.querySelector('input[name="password"], input[type="password"], #password');
        if (passwordInput) {
            this.fillInput(passwordInput, credentials.password);
            await this.delay(500);
        }
        
        console.log('Login form filled successfully');
    }

    /**
     * Fill application form with provided data
     */
    async fillApplicationForm(appData) {
        console.log('Filling application form...');
        
        // Fill web file ID
        if (appData.webFileId) {
            const webFileInput = document.querySelector('input[name="webfile_id"], #webfile_id');
            if (webFileInput) {
                this.fillInput(webFileInput, appData.webFileId);
                await this.delay(300);
            }
        }
        
        // Fill applicant name
        if (appData.applicantName) {
            const nameInput = document.querySelector('input[name="full__name"], input[name="name"], #full_name');
            if (nameInput) {
                this.fillInput(nameInput, appData.applicantName);
                await this.delay(300);
            }
        }
        
        // Fill email
        if (appData.email) {
            const emailInput = document.querySelector('input[name="email_name"], input[type="email"], #email');
            if (emailInput) {
                this.fillInput(emailInput, appData.email);
                await this.delay(300);
            }
        }
        
        // Select visa type
        if (appData.visaType) {
            const visaSelect = document.querySelector('select[name="visa_type"], #visa_type');
            if (visaSelect) {
                visaSelect.value = appData.visaType;
                visaSelect.dispatchEvent(new Event('change', { bubbles: true }));
                await this.delay(300);
            }
        }
        
        // Select IVAC center
        if (appData.ivacCenter) {
            const ivacSelect = document.querySelector('select[name="ivac_id"], #ivac_id');
            if (ivacSelect) {
                ivacSelect.value = appData.ivacCenter;
                ivacSelect.dispatchEvent(new Event('change', { bubbles: true }));
                await this.delay(300);
            }
        }
        
        // Fill visit reason
        if (appData.visitReason) {
            const reasonInput = document.querySelector('input[name="visit_purpose"], #visit_purpose, textarea[name="visit_purpose"]');
            if (reasonInput) {
                this.fillInput(reasonInput, appData.visitReason);
                await this.delay(300);
            }
        }
        
        console.log('Application form filled successfully');
    }

    /**
     * Select appointment date
     */
    async selectDate(date) {
        console.log('Selecting date:', date);
        
        // Look for date picker input
        const dateInput = document.querySelector('input[type="date"], input[name="appointment_date"], #appointment_date');
        if (dateInput) {
            this.fillInput(dateInput, date);
            await this.delay(500);
            return;
        }
        
        // Look for date picker calendar
        const datePicker = document.querySelector('.datepicker, .calendar, [data-date]');
        if (datePicker) {
            // Try to click on the specific date
            const dateElements = datePicker.querySelectorAll('[data-date], .day, .date');
            for (const element of dateElements) {
                if (element.textContent.includes(date) || element.getAttribute('data-date') === date) {
                    element.click();
                    await this.delay(500);
                    break;
                }
            }
        }
        
        console.log('Date selected successfully');
    }

    /**
     * Get reCAPTCHA details from the page
     */
    async getRecaptchaDetails() {
        console.log('Getting reCAPTCHA details...');
        
        // Look for reCAPTCHA iframe or container
        const recaptchaFrame = document.querySelector('iframe[src*="recaptcha"], .g-recaptcha');
        if (!recaptchaFrame) {
            throw new Error('reCAPTCHA not found on page');
        }
        
        // Extract site key
        let siteKey = null;
        
        // Try to get from data attribute
        const recaptchaContainer = document.querySelector('[data-sitekey]');
        if (recaptchaContainer) {
            siteKey = recaptchaContainer.getAttribute('data-sitekey');
        }
        
        // Try to get from script tags
        if (!siteKey) {
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                const match = script.textContent.match(/sitekey['"]\s*:\s*['"]([^'"]+)['"]/i);
                if (match) {
                    siteKey = match[1];
                    break;
                }
            }
        }
        
        // Try to get from global variables
        if (!siteKey && window.grecaptcha) {
            const widgets = document.querySelectorAll('.g-recaptcha');
            if (widgets.length > 0) {
                siteKey = widgets[0].getAttribute('data-sitekey');
            }
        }
        
        if (!siteKey) {
            throw new Error('Could not extract reCAPTCHA site key');
        }
        
        console.log('reCAPTCHA details extracted:', { siteKey, pageUrl: window.location.href });
        
        return {
            siteKey: siteKey,
            pageUrl: window.location.href
        };
    }

    /**
     * Submit reCAPTCHA token to the page
     */
    async submitRecaptchaToken(token) {
        console.log('Submitting reCAPTCHA token...');
        
        // Look for reCAPTCHA response textarea
        let responseTextarea = document.querySelector('textarea[name="g-recaptcha-response"]');
        if (responseTextarea) {
            responseTextarea.value = token;
            responseTextarea.dispatchEvent(new Event('input', { bubbles: true }));
            responseTextarea.dispatchEvent(new Event('change', { bubbles: true }));
        }
        
        // Try to trigger callback if available
        if (window.grecaptcha && window.grecaptcha.getResponse) {
            try {
                // Find widget ID and execute callback
                const widgets = document.querySelectorAll('.g-recaptcha');
                for (let i = 0; i < widgets.length; i++) {
                    const callback = widgets[i].getAttribute('data-callback');
                    if (callback && window[callback]) {
                        window[callback](token);
                        break;
                    }
                }
            } catch (error) {
                console.warn('Could not trigger reCAPTCHA callback:', error);
            }
        }
        
        // Submit form if auto-submit is enabled
        const form = document.querySelector('form');
        if (form) {
            const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitButton && !submitButton.disabled) {
                await this.delay(1000); // Wait a bit before submitting
                submitButton.click();
            }
        }
        
        console.log('reCAPTCHA token submitted successfully');
    }

    /**
     * Get current page information
     */
    async getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            hasRecaptcha: !!document.querySelector('iframe[src*="recaptcha"], .g-recaptcha'),
            hasLoginForm: !!document.querySelector('input[type="password"]'),
            hasApplicationForm: !!document.querySelector('input[name="webfile_id"], #webfile_id'),
            csrfToken: this.extractCSRFToken(),
            currentStep: this.detectCurrentStep()
        };
    }

    /**
     * Click an element by selector
     */
    async clickElement(selector) {
        const element = document.querySelector(selector);
        if (!element) {
            throw new Error(`Element not found: ${selector}`);
        }
        
        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await this.delay(500);
        
        // Click the element
        element.click();
        await this.delay(300);
        
        console.log('Element clicked:', selector);
    }

    /**
     * Wait for element to appear
     */
    async waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
            await this.delay(100);
        }

        throw new Error(`Element not found within timeout: ${selector}`);
    }

    /**
     * Extract data from page using selectors
     */
    async extractData(selectors) {
        const data = {};

        for (const [key, selector] of Object.entries(selectors)) {
            const element = document.querySelector(selector);
            if (element) {
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    data[key] = element.value;
                } else if (element.tagName === 'SELECT') {
                    data[key] = element.selectedOptions[0]?.value || '';
                } else {
                    data[key] = element.textContent.trim();
                }
            } else {
                data[key] = null;
            }
        }

        return data;
    }

    /**
     * Fill input field with value
     */
    fillInput(input, value) {
        // Clear existing value
        input.value = '';
        input.focus();

        // Set new value
        input.value = value;

        // Trigger events
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));
    }

    /**
     * Extract CSRF token from page
     */
    extractCSRFToken() {
        // Look for CSRF token in meta tags
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            return metaToken.getAttribute('content');
        }

        // Look for CSRF token in hidden inputs
        const hiddenToken = document.querySelector('input[name="_token"], input[name="csrf_token"]');
        if (hiddenToken) {
            return hiddenToken.value;
        }

        // Look for CSRF token in global variables
        if (window.csrf_token) {
            return window.csrf_token;
        }

        // Look for CSRF token in scripts
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
            const match = script.textContent.match(/csrf[_-]?token['"]\s*:\s*['"]([^'"]+)['"]/i);
            if (match) {
                return match[1];
            }
        }

        return null;
    }

    /**
     * Detect current step in the process
     */
    detectCurrentStep() {
        const url = window.location.href;
        const body = document.body.innerHTML.toLowerCase();

        if (url.includes('login') || body.includes('login') || document.querySelector('input[type="password"]')) {
            return 'login';
        } else if (url.includes('application') || body.includes('application') || document.querySelector('input[name="webfile_id"]')) {
            return 'application';
        } else if (url.includes('payment') || body.includes('payment') || body.includes('pay')) {
            return 'payment';
        } else if (url.includes('success') || body.includes('success') || body.includes('complete')) {
            return 'complete';
        }

        return 'unknown';
    }

    /**
     * Utility function to delay execution
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Submit form with data
     */
    async submitForm(formData) {
        const form = document.querySelector('form');
        if (!form) {
            throw new Error('No form found on page');
        }

        // Fill form fields
        for (const [name, value] of Object.entries(formData)) {
            const input = form.querySelector(`[name="${name}"]`);
            if (input) {
                this.fillInput(input, value);
                await this.delay(100);
            }
        }

        // Submit form
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitButton) {
            submitButton.click();
        } else {
            form.submit();
        }

        console.log('Form submitted with data:', formData);
    }

    /**
     * Handle OTP input
     */
    async fillOTP(otp) {
        console.log('Filling OTP:', otp);

        // Look for OTP input fields
        const otpInputs = document.querySelectorAll('input[name*="otp"], input[type="number"][maxlength="1"], .otp-input');

        if (otpInputs.length === 1) {
            // Single OTP input
            this.fillInput(otpInputs[0], otp);
        } else if (otpInputs.length > 1) {
            // Multiple OTP inputs (one digit each)
            const otpDigits = otp.toString().split('');
            for (let i = 0; i < Math.min(otpInputs.length, otpDigits.length); i++) {
                this.fillInput(otpInputs[i], otpDigits[i]);
                await this.delay(100);
            }
        } else {
            // Look for general input that might be OTP
            const generalOtpInput = document.querySelector('input[placeholder*="OTP"], input[placeholder*="code"]');
            if (generalOtpInput) {
                this.fillInput(generalOtpInput, otp);
            } else {
                throw new Error('OTP input field not found');
            }
        }

        console.log('OTP filled successfully');
    }

    /**
     * Get payment link from page
     */
    extractPaymentLink() {
        // Look for payment links in various formats
        const linkSelectors = [
            'a[href*="payment"]',
            'a[href*="pay"]',
            'a[href*="invoice"]',
            '.payment-link',
            '.invoice-link'
        ];

        for (const selector of linkSelectors) {
            const link = document.querySelector(selector);
            if (link) {
                return link.href;
            }
        }

        // Look for payment URL in text content
        const paymentUrlMatch = document.body.innerHTML.match(/(https?:\/\/[^\s<>"']+(?:payment|pay|invoice)[^\s<>"']*)/i);
        if (paymentUrlMatch) {
            return paymentUrlMatch[1];
        }

        return null;
    }
}

// Initialize content script
new IVACContentScript();
