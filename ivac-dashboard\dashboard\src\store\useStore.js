import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import api from '../services/api'

const useStore = create(
  devtools(
    (set, get) => ({
      // Files state
      files: [],
      filesLoading: false,
      filesError: null,
      pagination: {
        current_page: 1,
        per_page: 20,
        total: 0,
        pages: 0
      },
      filters: {
        status: '',
        search: ''
      },

      // UI state
      sidebarOpen: true,
      selectedFiles: [],

      // Actions
      setSidebarOpen: (open) => set({ sidebarOpen: open }),

      // Files actions
      setFiles: (files) => set({ files }),
      setFilesLoading: (loading) => set({ filesLoading: loading }),
      setFilesError: (error) => set({ filesError: error }),
      setPagination: (pagination) => set({ pagination }),
      setFilters: (filters) => set({ filters: { ...get().filters, ...filters } }),

      // Fetch files
      fetchFiles: async (page = 1, filters = {}) => {
        set({ filesLoading: true, filesError: null })
        
        try {
          const params = {
            page,
            per_page: get().pagination.per_page,
            ...get().filters,
            ...filters
          }

          const response = await api.get('/files', { params })
          
          set({
            files: response.data.files,
            pagination: {
              current_page: response.data.current_page,
              per_page: response.data.per_page,
              total: response.data.total,
              pages: response.data.pages
            },
            filesLoading: false
          })
        } catch (error) {
          set({
            filesError: error.response?.data?.message || 'Failed to fetch files',
            filesLoading: false
          })
        }
      },

      // Create file
      createFile: async (fileData) => {
        try {
          const response = await api.post('/files', fileData)
          
          // Refresh files list
          await get().fetchFiles()
          
          return response.data
        } catch (error) {
          throw new Error(error.response?.data?.message || 'Failed to create file')
        }
      },

      // Update file
      updateFile: async (id, updates) => {
        try {
          const response = await api.put(`/files/${id}`, updates)
          
          // Update file in local state
          const files = get().files.map(file => 
            file.id === id ? { ...file, ...response.data.file } : file
          )
          set({ files })
          
          return response.data
        } catch (error) {
          throw new Error(error.response?.data?.message || 'Failed to update file')
        }
      },

      // Delete file
      deleteFile: async (id) => {
        try {
          await api.delete(`/files/${id}`)
          
          // Remove file from local state
          const files = get().files.filter(file => file.id !== id)
          set({ files })
          
          return true
        } catch (error) {
          throw new Error(error.response?.data?.message || 'Failed to delete file')
        }
      },

      // Bulk actions
      setSelectedFiles: (selectedFiles) => set({ selectedFiles }),
      
      bulkUpdateStatus: async (fileIds, status) => {
        try {
          const promises = fileIds.map(id => 
            api.put(`/files/${id}`, { status })
          )
          
          await Promise.all(promises)
          
          // Refresh files list
          await get().fetchFiles()
          
          return true
        } catch (error) {
          throw new Error('Failed to update files')
        }
      },

      bulkDelete: async (fileIds) => {
        try {
          const promises = fileIds.map(id => api.delete(`/files/${id}`))
          await Promise.all(promises)
          
          // Remove files from local state
          const files = get().files.filter(file => !fileIds.includes(file.id))
          set({ files, selectedFiles: [] })
          
          return true
        } catch (error) {
          throw new Error('Failed to delete files')
        }
      },

      // Search and filter
      searchFiles: async (searchTerm) => {
        set({ filters: { ...get().filters, search: searchTerm } })
        await get().fetchFiles(1, { search: searchTerm })
      },

      filterByStatus: async (status) => {
        set({ filters: { ...get().filters, status } })
        await get().fetchFiles(1, { status })
      },

      // Reset filters
      resetFilters: async () => {
        set({ 
          filters: { status: '', search: '' }
        })
        await get().fetchFiles(1)
      },

      // Pagination
      goToPage: async (page) => {
        await get().fetchFiles(page)
      },

      // Statistics (could be expanded)
      getStats: () => {
        const files = get().files
        const stats = {
          total: files.length,
          pending: files.filter(f => f.status === 'pending').length,
          processing: files.filter(f => f.status === 'processing').length,
          completed: files.filter(f => f.status === 'completed').length,
          error: files.filter(f => f.status === 'error').length,
        }
        return stats
      }
    }),
    {
      name: 'ivac-dashboard-store',
    }
  )
)

export default useStore
