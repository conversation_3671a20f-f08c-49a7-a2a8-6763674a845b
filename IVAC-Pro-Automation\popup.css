/* IVAC Pro Automation - Popup Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    width: 380px;
    height: 600px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
}

.popup-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    margin: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Header */
.popup-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 16px;
    border-radius: 12px 12px 0 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.logo-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
}

.logo h1 {
    font-size: 1.2rem;
    font-weight: 700;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.popup-main {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* Control Panel */
.control-panel {
    margin-bottom: 20px;
}

.main-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;
}

.toggle-button {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.toggle-button.start {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.toggle-button.stop {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.toggle-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.settings-button {
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.85rem;
}

.settings-button:hover {
    border-color: #4f46e5;
    color: #4f46e5;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.stat-item {
    background: #f8fafc;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #64748b;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 1rem;
    font-weight: 700;
    color: #1e293b;
}

/* Progress Section */
.progress-section {
    margin-bottom: 20px;
}

.progress-section h3 {
    font-size: 0.9rem;
    color: #374151;
    margin-bottom: 12px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 16px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    width: 0%;
    transition: width 0.5s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    flex: 1;
}

.step-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.step.active .step-icon {
    background: #4f46e5;
    color: white;
}

.step.completed .step-icon {
    background: #10b981;
    color: white;
}

.step-label {
    font-size: 0.7rem;
    color: #6b7280;
    text-align: center;
}

.step.active .step-label,
.step.completed .step-label {
    color: #374151;
    font-weight: 600;
}

/* Log Section */
.log-section {
    margin-bottom: 20px;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.log-header h3 {
    font-size: 0.9rem;
    color: #374151;
}

.log-controls {
    display: flex;
    gap: 6px;
}

.clear-log-button,
.export-log-button {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    font-size: 0.7rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-log-button:hover,
.export-log-button:hover {
    background: #f3f4f6;
}

.log-container {
    height: 120px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
}

.log-entry {
    display: flex;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 0.7rem;
    line-height: 1.4;
}

.log-time {
    color: #6b7280;
    font-weight: 600;
    min-width: 60px;
}

.log-message {
    flex: 1;
}

.log-entry.info .log-message {
    color: #1f2937;
}

.log-entry.success .log-message {
    color: #059669;
}

.log-entry.warning .log-message {
    color: #d97706;
}

.log-entry.error .log-message {
    color: #dc2626;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.action-button {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.action-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-button.pause {
    background: #fbbf24;
    color: #92400e;
}

.action-button.skip {
    background: #60a5fa;
    color: #1e40af;
}

.action-button.emergency {
    background: #f87171;
    color: #991b1b;
}

.action-button:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Footer */
.popup-footer {
    padding: 12px 16px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #6b7280;
    border-radius: 0 0 12px 12px;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.separator {
    color: #d1d5db;
}

.connection-status {
    font-weight: 600;
}

.connection-status.connected {
    color: #059669;
}

.connection-status.disconnected {
    color: #dc2626;
}

.footer-links {
    display: flex;
    gap: 12px;
}

.footer-links a {
    color: #6b7280;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: #4f46e5;
}

/* Scrollbar Styling */
.popup-main::-webkit-scrollbar,
.log-container::-webkit-scrollbar {
    width: 4px;
}

.popup-main::-webkit-scrollbar-track,
.log-container::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.popup-main::-webkit-scrollbar-thumb,
.log-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.popup-main::-webkit-scrollbar-thumb:hover,
.log-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
