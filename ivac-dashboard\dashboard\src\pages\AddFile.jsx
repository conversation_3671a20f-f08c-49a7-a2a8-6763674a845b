import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Plus, Trash2, Save, ArrowLeft } from 'lucide-react'
import toast from 'react-hot-toast'
import useStore from '../store/useStore'

const AddFile = () => {
  const navigate = useNavigate()
  const { createFile } = useStore()
  
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    web_id: '',
    web_name: '',
    email: '',
    phone: '',
    password: '',
    location: '',
    ivac_center: '',
    visa_type: '',
    visa_category: '',
    payment_method: '',
    family_members: []
  })

  const [errors, setErrors] = useState({})

  // Location and center options
  const locations = [
    'Dhaka',
    'Chittagong',
    'Sylhet',
    'Rajshahi',
    'Khulna',
    'Barisal',
    'Rangpur',
    'Mymensingh'
  ]

  const ivacCenters = {
    'Dhaka': ['Dhaka IVAC', 'Uttara IVAC', '<PERSON>hanmondi IVAC'],
    'Chittagong': ['Chittagong IVAC', 'Agrabad IVAC'],
    'Sylhet': ['Sylhet IVAC'],
    'Rajshahi': ['Rajshahi IVAC'],
    'Khulna': ['Khulna IVAC'],
    'Barisal': ['Barisal IVAC'],
    'Rangpur': ['Rangpur IVAC'],
    'Mymensingh': ['Mymensingh IVAC']
  }

  const visaTypes = [
    'Tourist Visa',
    'Business Visa',
    'Medical Visa',
    'Student Visa',
    'Employment Visa',
    'Transit Visa',
    'Conference Visa',
    'Journalist Visa'
  ]

  const visaCategories = [
    'Single Entry',
    'Double Entry',
    'Multiple Entry',
    'Long Term Multiple Entry'
  ]

  const paymentMethods = [
    'Credit Card',
    'Debit Card',
    'Bank Transfer',
    'Mobile Banking',
    'Cash'
  ]

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  // Handle location change
  const handleLocationChange = (e) => {
    const location = e.target.value
    setFormData(prev => ({
      ...prev,
      location,
      ivac_center: '' // Reset center when location changes
    }))
  }

  // Add family member
  const addFamilyMember = () => {
    setFormData(prev => ({
      ...prev,
      family_members: [
        ...prev.family_members,
        {
          name: '',
          relationship: '',
          passport_number: '',
          date_of_birth: ''
        }
      ]
    }))
  }

  // Remove family member
  const removeFamilyMember = (index) => {
    setFormData(prev => ({
      ...prev,
      family_members: prev.family_members.filter((_, i) => i !== index)
    }))
  }

  // Handle family member change
  const handleFamilyMemberChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      family_members: prev.family_members.map((member, i) => 
        i === index ? { ...member, [field]: value } : member
      )
    }))
  }

  // Validate form
  const validateForm = () => {
    const newErrors = {}

    // Required fields
    const requiredFields = [
      'name', 'web_id', 'email', 'phone', 'password', 
      'location', 'ivac_center', 'visa_type', 'visa_category'
    ]

    requiredFields.forEach(field => {
      if (!formData[field]?.trim()) {
        newErrors[field] = 'This field is required'
      }
    })

    // Email validation
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Phone validation
    if (formData.phone && !/^[\d\s\-\+\(\)]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form')
      return
    }

    setLoading(true)
    
    try {
      await createFile(formData)
      toast.success('File created successfully!')
      navigate('/dashboard')
    } catch (error) {
      toast.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/dashboard')}
            className="btn-outline"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Add New Visa File</h2>
            <p className="text-gray-600">Create a new IVAC visa application file</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">Full Name *</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`input ${errors.name ? 'border-red-500' : ''}`}
                placeholder="Enter full name"
              />
              {errors.name && <p className="form-error">{errors.name}</p>}
            </div>

            <div className="form-group">
              <label className="form-label">Email Address *</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`input ${errors.email ? 'border-red-500' : ''}`}
                placeholder="Enter email address"
              />
              {errors.email && <p className="form-error">{errors.email}</p>}
            </div>

            <div className="form-group">
              <label className="form-label">Phone Number *</label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={`input ${errors.phone ? 'border-red-500' : ''}`}
                placeholder="Enter phone number"
              />
              {errors.phone && <p className="form-error">{errors.phone}</p>}
            </div>

            <div className="form-group">
              <label className="form-label">Password *</label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className={`input ${errors.password ? 'border-red-500' : ''}`}
                placeholder="Enter password"
              />
              {errors.password && <p className="form-error">{errors.password}</p>}
            </div>
          </div>
        </div>

        {/* Web Information */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Web Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">Web ID *</label>
              <input
                type="text"
                name="web_id"
                value={formData.web_id}
                onChange={handleInputChange}
                className={`input ${errors.web_id ? 'border-red-500' : ''}`}
                placeholder="Enter web ID"
              />
              {errors.web_id && <p className="form-error">{errors.web_id}</p>}
            </div>

            <div className="form-group">
              <label className="form-label">Web Name</label>
              <input
                type="text"
                name="web_name"
                value={formData.web_name}
                onChange={handleInputChange}
                className="input"
                placeholder="Enter web name (optional)"
              />
            </div>
          </div>
        </div>

        {/* Location Information */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Location Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">Location *</label>
              <select
                name="location"
                value={formData.location}
                onChange={handleLocationChange}
                className={`select ${errors.location ? 'border-red-500' : ''}`}
              >
                <option value="">Select Location</option>
                {locations.map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
              {errors.location && <p className="form-error">{errors.location}</p>}
            </div>

            <div className="form-group">
              <label className="form-label">IVAC Center *</label>
              <select
                name="ivac_center"
                value={formData.ivac_center}
                onChange={handleInputChange}
                className={`select ${errors.ivac_center ? 'border-red-500' : ''}`}
                disabled={!formData.location}
              >
                <option value="">Select IVAC Center</option>
                {formData.location && ivacCenters[formData.location]?.map(center => (
                  <option key={center} value={center}>{center}</option>
                ))}
              </select>
              {errors.ivac_center && <p className="form-error">{errors.ivac_center}</p>}
            </div>
          </div>
        </div>

        {/* Visa Information */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Visa Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">Visa Type *</label>
              <select
                name="visa_type"
                value={formData.visa_type}
                onChange={handleInputChange}
                className={`select ${errors.visa_type ? 'border-red-500' : ''}`}
              >
                <option value="">Select Visa Type</option>
                {visaTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              {errors.visa_type && <p className="form-error">{errors.visa_type}</p>}
            </div>

            <div className="form-group">
              <label className="form-label">Visa Category *</label>
              <select
                name="visa_category"
                value={formData.visa_category}
                onChange={handleInputChange}
                className={`select ${errors.visa_category ? 'border-red-500' : ''}`}
              >
                <option value="">Select Visa Category</option>
                {visaCategories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              {errors.visa_category && <p className="form-error">{errors.visa_category}</p>}
            </div>

            <div className="form-group">
              <label className="form-label">Payment Method</label>
              <select
                name="payment_method"
                value={formData.payment_method}
                onChange={handleInputChange}
                className="select"
              >
                <option value="">Select Payment Method</option>
                {paymentMethods.map(method => (
                  <option key={method} value={method}>{method}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Family Members */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Family Members</h3>
            <button
              type="button"
              onClick={addFamilyMember}
              className="btn-outline btn-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Family Member
            </button>
          </div>

          {formData.family_members.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No family members added</p>
          ) : (
            <div className="space-y-4">
              {formData.family_members.map((member, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900">Family Member {index + 1}</h4>
                    <button
                      type="button"
                      onClick={() => removeFamilyMember(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="form-group">
                      <label className="form-label">Name</label>
                      <input
                        type="text"
                        value={member.name}
                        onChange={(e) => handleFamilyMemberChange(index, 'name', e.target.value)}
                        className="input"
                        placeholder="Enter name"
                      />
                    </div>

                    <div className="form-group">
                      <label className="form-label">Relationship</label>
                      <select
                        value={member.relationship}
                        onChange={(e) => handleFamilyMemberChange(index, 'relationship', e.target.value)}
                        className="select"
                      >
                        <option value="">Select Relationship</option>
                        <option value="spouse">Spouse</option>
                        <option value="child">Child</option>
                        <option value="parent">Parent</option>
                        <option value="sibling">Sibling</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div className="form-group">
                      <label className="form-label">Passport Number</label>
                      <input
                        type="text"
                        value={member.passport_number}
                        onChange={(e) => handleFamilyMemberChange(index, 'passport_number', e.target.value)}
                        className="input"
                        placeholder="Enter passport number"
                      />
                    </div>

                    <div className="form-group">
                      <label className="form-label">Date of Birth</label>
                      <input
                        type="date"
                        value={member.date_of_birth}
                        onChange={(e) => handleFamilyMemberChange(index, 'date_of_birth', e.target.value)}
                        className="input"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex items-center justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/dashboard')}
            className="btn-outline"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-primary"
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="loading-spinner h-4 w-4 mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create File
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}

export default AddFile
