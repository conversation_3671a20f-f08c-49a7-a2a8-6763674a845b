<?php
/**
 * Plugin Name: IVAC Dashboard
 * Plugin URI: https://example.com/ivac-dashboard
 * Description: A comprehensive WordPress plugin with React-based dashboard and REST API to manage and automate IVAC visa applications.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 * Text Domain: ivac-dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('IVAC_DASHBOARD_VERSION', '1.0.0');
define('IVAC_DASHBOARD_PLUGIN_URL', plugin_dir_url(__FILE__));
define('IVAC_DASHBOARD_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main IVAC Dashboard Plugin Class
 */
class IVAC_Dashboard_Plugin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Load plugin textdomain
        load_plugin_textdomain('ivac-dashboard', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize components
        $this->init_database();
        $this->init_shortcodes();
        $this->init_rest_api();
        $this->init_admin();
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        $this->create_database_tables();
        $this->create_default_options();
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        flush_rewrite_rules();
    }
    
    /**
     * Initialize database tables
     */
    private function init_database() {
        // Database operations will be handled here
    }
    
    /**
     * Create database tables
     */
    private function create_database_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // IVAC Files table
        $table_name = $wpdb->prefix . 'ivac_files';
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            web_id varchar(100) NOT NULL,
            web_name varchar(255) DEFAULT '',
            email varchar(255) NOT NULL,
            phone varchar(20) NOT NULL,
            password varchar(255) NOT NULL,
            location varchar(50) NOT NULL,
            ivac_center varchar(100) NOT NULL,
            visa_type varchar(100) NOT NULL,
            visa_category varchar(100) NOT NULL,
            family_members longtext DEFAULT NULL,
            status varchar(20) DEFAULT 'pending',
            payment_link varchar(500) DEFAULT '',
            payment_method varchar(50) DEFAULT '',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            processed_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // API Keys table
        $api_table_name = $wpdb->prefix . 'ivac_api_keys';
        $api_sql = "CREATE TABLE $api_table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            api_key varchar(64) NOT NULL,
            name varchar(255) NOT NULL,
            permissions longtext DEFAULT NULL,
            last_used datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY (id),
            UNIQUE KEY api_key (api_key),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        dbDelta($api_sql);
    }
    
    /**
     * Create default options
     */
    private function create_default_options() {
        add_option('ivac_dashboard_version', IVAC_DASHBOARD_VERSION);
        add_option('ivac_dashboard_settings', array(
            'enable_notifications' => true,
            'auto_process_jobs' => false,
            'max_concurrent_jobs' => 5
        ));
    }
    
    /**
     * Initialize shortcodes
     */
    private function init_shortcodes() {
        add_shortcode('ivac_dashboard', array($this, 'render_dashboard_shortcode'));
    }
    
    /**
     * Render the dashboard shortcode
     */
    public function render_dashboard_shortcode($atts) {
        // Check if user has permission to view dashboard
        if (!current_user_can('manage_options')) {
            return '<p>' . __('You do not have permission to access this dashboard.', 'ivac-dashboard') . '</p>';
        }
        
        // Enqueue React app scripts
        $this->enqueue_dashboard_scripts();
        
        // Return the root div for React app
        return '<div id="ivac-react-dashboard-root" class="ivac-dashboard-container"></div>';
    }
    
    /**
     * Initialize REST API
     */
    private function init_rest_api() {
        add_action('rest_api_init', array($this, 'register_rest_routes'));
    }
    
    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        // Include REST API classes
        require_once IVAC_DASHBOARD_PLUGIN_PATH . 'includes/class-ivac-rest-api.php';
        require_once IVAC_DASHBOARD_PLUGIN_PATH . 'includes/class-ivac-auth-api.php';
        require_once IVAC_DASHBOARD_PLUGIN_PATH . 'includes/class-ivac-jobs-api.php';
        
        // Initialize API classes
        new IVAC_REST_API();
        new IVAC_Auth_API();
        new IVAC_Jobs_API();
    }
    
    /**
     * Initialize admin area
     */
    private function init_admin() {
        if (is_admin()) {
            require_once IVAC_DASHBOARD_PLUGIN_PATH . 'includes/class-ivac-admin.php';
            new IVAC_Admin();
        }
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        // Only enqueue on pages with the shortcode
        global $post;
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'ivac_dashboard')) {
            $this->enqueue_dashboard_scripts();
        }
    }
    
    /**
     * Enqueue dashboard-specific scripts
     */
    private function enqueue_dashboard_scripts() {
        // Check if build files exist
        $js_file = IVAC_DASHBOARD_PLUGIN_PATH . 'dashboard/dist/assets/index.js';
        $css_file = IVAC_DASHBOARD_PLUGIN_PATH . 'dashboard/dist/assets/index.css';
        
        if (file_exists($js_file)) {
            wp_enqueue_script(
                'ivac-dashboard-app',
                IVAC_DASHBOARD_PLUGIN_URL . 'dashboard/dist/assets/index.js',
                array(),
                IVAC_DASHBOARD_VERSION,
                true
            );

            // Localize script with API data
            $current_user = wp_get_current_user();
            wp_localize_script('ivac-dashboard-app', 'ivacDashboard', array(
                'apiUrl' => rest_url('ivac/v1/'),
                'nonce' => wp_create_nonce('wp_rest'),
                'currentUser' => array(
                    'id' => $current_user->ID,
                    'name' => $current_user->display_name,
                    'email' => $current_user->user_email,
                ),
                'pluginUrl' => IVAC_DASHBOARD_PLUGIN_URL,
                'wpVersion' => get_bloginfo('version'),
                'phpVersion' => PHP_VERSION,
                'dbVersion' => $GLOBALS['wp_version'],
            ));
        } else {
            // Development mode - show build instructions
            wp_enqueue_script(
                'ivac-dashboard-dev-notice',
                IVAC_DASHBOARD_PLUGIN_URL . 'assets/dev-notice.js',
                array(),
                IVAC_DASHBOARD_VERSION,
                true
            );
        }
        
        if (file_exists($css_file)) {
            wp_enqueue_style(
                'ivac-dashboard-style',
                IVAC_DASHBOARD_PLUGIN_URL . 'dashboard/dist/assets/index.css',
                array(),
                IVAC_DASHBOARD_VERSION
            );
        }
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our admin pages
        if (strpos($hook, 'ivac-dashboard') !== false) {
            wp_enqueue_style('ivac-dashboard-admin', IVAC_DASHBOARD_PLUGIN_URL . 'assets/admin.css', array(), IVAC_DASHBOARD_VERSION);
            wp_enqueue_script('ivac-dashboard-admin', IVAC_DASHBOARD_PLUGIN_URL . 'assets/admin.js', array('jquery'), IVAC_DASHBOARD_VERSION, true);
        }
    }
}

// Initialize the plugin
new IVAC_Dashboard_Plugin();
