# IVAC Dashboard WordPress Plugin

A comprehensive WordPress plugin for managing and automating IVAC (Indian Visa Application Center) visa applications with a React-based dashboard interface.

## Features

- **React Dashboard**: Modern, responsive dashboard built with React, Vite, and Tailwind CSS
- **REST API**: Custom WordPress REST API endpoints for file management and Chrome extension integration
- **Authentication**: API key-based authentication system for secure Chrome extension access
- **File Management**: Complete CRUD operations for visa application files
- **Job Processing**: Queue system for automated visa application processing
- **Admin Interface**: WordPress admin integration with settings and API key management

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Build the React dashboard (see Development Setup below)
4. Use the shortcode `[ivac_dashboard]` on any page or post

## Development Setup

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- WordPress development environment

### Building the React Dashboard

1. Navigate to the dashboard directory:
   ```bash
   cd wp-content/plugins/ivac-dashboard/dashboard
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build for production:
   ```bash
   npm run build
   ```

4. Or run in development mode:
   ```bash
   npm run dev
   ```

### Development Commands

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Plugin Structure

```
ivac-dashboard/
├── ivac-dashboard.php          # Main plugin file
├── includes/                   # PHP classes
│   ├── class-ivac-rest-api.php # Files REST API
│   ├── class-ivac-auth-api.php # Authentication API
│   ├── class-ivac-jobs-api.php # Jobs API
│   └── class-ivac-admin.php    # Admin interface
├── dashboard/                  # React application
│   ├── src/
│   │   ├── components/         # React components
│   │   ├── pages/             # Page components
│   │   ├── services/          # API services
│   │   └── store/             # State management
│   ├── dist/                  # Built files (generated)
│   └── package.json           # Node.js dependencies
├── assets/                    # Static assets
└── README.md                  # This file
```

## REST API Endpoints

### Files Management
- `GET /wp-json/ivac/v1/files` - Get all files
- `POST /wp-json/ivac/v1/files` - Create new file
- `GET /wp-json/ivac/v1/files/{id}` - Get single file
- `PUT /wp-json/ivac/v1/files/{id}` - Update file
- `DELETE /wp-json/ivac/v1/files/{id}` - Delete file

### Authentication
- `POST /wp-json/ivac/v1/auth` - Validate API key
- `POST /wp-json/ivac/v1/auth/generate-key` - Generate new API key
- `GET /wp-json/ivac/v1/auth/keys` - Get API keys
- `DELETE /wp-json/ivac/v1/auth/keys/{id}` - Revoke API key

### Jobs (Chrome Extension)
- `GET /wp-json/ivac/v1/jobs/next` - Get next pending job
- `POST /wp-json/ivac/v1/jobs/update` - Update job status
- `GET /wp-json/ivac/v1/jobs/stats` - Get job statistics
- `POST /wp-json/ivac/v1/jobs/{id}/reset` - Reset stuck job

## Usage

### Dashboard Access

1. Add the shortcode `[ivac_dashboard]` to any page or post
2. Users with `manage_options` capability can access the dashboard
3. The dashboard provides a complete interface for managing visa files

### Chrome Extension Integration

1. Generate an API key from the dashboard
2. Configure the Chrome extension with the API key
3. The extension will automatically process pending jobs

### Admin Interface

Access the admin interface through:
- **WordPress Admin > IVAC Dashboard** - Main dashboard
- **WordPress Admin > IVAC Dashboard > Settings** - Plugin settings
- **WordPress Admin > IVAC Dashboard > API Keys** - API key management

## Database Tables

The plugin creates two custom tables:

### `wp_ivac_files`
Stores visa application files with personal information, visa details, and processing status.

### `wp_ivac_api_keys`
Stores API keys for Chrome extension authentication.

## Security

- API key-based authentication for Chrome extension
- WordPress nonce verification for admin requests
- Capability checks for dashboard access
- Input sanitization and validation
- SQL injection prevention with prepared statements

## Customization

### Styling
The React dashboard uses Tailwind CSS for styling. You can customize the appearance by:
1. Modifying the Tailwind configuration in `dashboard/tailwind.config.js`
2. Adding custom CSS classes in `dashboard/src/index.css`
3. Updating component styles in individual React components

### API Extensions
Add new REST API endpoints by:
1. Creating new PHP classes in the `includes/` directory
2. Registering routes in the class constructor
3. Including the class in the main plugin file

## Troubleshooting

### Dashboard Not Loading
- Ensure the React app is built (`npm run build`)
- Check browser console for JavaScript errors
- Verify WordPress REST API is accessible

### API Authentication Issues
- Verify API key is correctly generated and active
- Check WordPress REST API permissions
- Ensure proper nonce verification

### Build Issues
- Update Node.js to v16 or higher
- Clear npm cache: `npm cache clean --force`
- Delete `node_modules` and reinstall: `rm -rf node_modules && npm install`

## Support

For support and bug reports, please check the plugin documentation or contact the development team.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- React dashboard with file management
- REST API endpoints
- Chrome extension integration
- Admin interface
- API key authentication system
