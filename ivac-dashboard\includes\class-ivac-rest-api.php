<?php
/**
 * IVAC Dashboard REST API - Files Management
 */

if (!defined('ABSPATH')) {
    exit;
}

class IVAC_REST_API {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->register_routes();
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Files endpoints
        register_rest_route('ivac/v1', '/files', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_files'),
                'permission_callback' => array($this, 'check_permissions'),
            ),
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array($this, 'create_file'),
                'permission_callback' => array($this, 'check_permissions'),
                'args' => $this->get_file_schema(),
            ),
        ));
        
        register_rest_route('ivac/v1', '/files/(?P<id>\d+)', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_file'),
                'permission_callback' => array($this, 'check_permissions'),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'update_file'),
                'permission_callback' => array($this, 'check_permissions'),
                'args' => $this->get_file_schema(),
            ),
            array(
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => array($this, 'delete_file'),
                'permission_callback' => array($this, 'check_permissions'),
            ),
        ));
    }
    
    /**
     * Check permissions for API access
     */
    public function check_permissions() {
        return current_user_can('manage_options');
    }
    
    /**
     * Get all files
     */
    public function get_files($request) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'ivac_files';
        $page = $request->get_param('page') ?: 1;
        $per_page = $request->get_param('per_page') ?: 20;
        $status = $request->get_param('status');
        $search = $request->get_param('search');
        
        $offset = ($page - 1) * $per_page;
        
        // Build query
        $where_conditions = array('1=1');
        $where_values = array();
        
        if ($status) {
            $where_conditions[] = 'status = %s';
            $where_values[] = $status;
        }
        
        if ($search) {
            $where_conditions[] = '(name LIKE %s OR email LIKE %s OR web_id LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // Get total count
        $count_query = "SELECT COUNT(*) FROM $table_name WHERE $where_clause";
        if (!empty($where_values)) {
            $count_query = $wpdb->prepare($count_query, $where_values);
        }
        $total = $wpdb->get_var($count_query);
        
        // Get files
        $query = "SELECT * FROM $table_name WHERE $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d";
        $query_values = array_merge($where_values, array($per_page, $offset));
        $files = $wpdb->get_results($wpdb->prepare($query, $query_values), ARRAY_A);
        
        // Process family members JSON
        foreach ($files as &$file) {
            if ($file['family_members']) {
                $file['family_members'] = json_decode($file['family_members'], true);
            } else {
                $file['family_members'] = array();
            }
        }
        
        return new WP_REST_Response(array(
            'files' => $files,
            'total' => (int) $total,
            'pages' => ceil($total / $per_page),
            'current_page' => (int) $page,
            'per_page' => (int) $per_page
        ), 200);
    }
    
    /**
     * Get single file
     */
    public function get_file($request) {
        global $wpdb;
        
        $id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'ivac_files';
        
        $file = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id), ARRAY_A);
        
        if (!$file) {
            return new WP_Error('file_not_found', 'File not found', array('status' => 404));
        }
        
        // Process family members JSON
        if ($file['family_members']) {
            $file['family_members'] = json_decode($file['family_members'], true);
        } else {
            $file['family_members'] = array();
        }
        
        return new WP_REST_Response($file, 200);
    }
    
    /**
     * Create new file
     */
    public function create_file($request) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'ivac_files';
        
        // Validate required fields
        $required_fields = array('name', 'web_id', 'email', 'phone', 'password', 'location', 'ivac_center', 'visa_type', 'visa_category');
        foreach ($required_fields as $field) {
            if (empty($request->get_param($field))) {
                return new WP_Error('missing_field', "Field '$field' is required", array('status' => 400));
            }
        }
        
        // Prepare data
        $data = array(
            'name' => sanitize_text_field($request->get_param('name')),
            'web_id' => sanitize_text_field($request->get_param('web_id')),
            'web_name' => sanitize_text_field($request->get_param('web_name')),
            'email' => sanitize_email($request->get_param('email')),
            'phone' => sanitize_text_field($request->get_param('phone')),
            'password' => $request->get_param('password'), // Store encrypted in production
            'location' => sanitize_text_field($request->get_param('location')),
            'ivac_center' => sanitize_text_field($request->get_param('ivac_center')),
            'visa_type' => sanitize_text_field($request->get_param('visa_type')),
            'visa_category' => sanitize_text_field($request->get_param('visa_category')),
            'payment_method' => sanitize_text_field($request->get_param('payment_method')),
            'status' => 'pending',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );
        
        // Handle family members
        $family_members = $request->get_param('family_members');
        if ($family_members && is_array($family_members)) {
            $data['family_members'] = json_encode($family_members);
        }
        
        // Insert into database
        $result = $wpdb->insert($table_name, $data);
        
        if ($result === false) {
            return new WP_Error('db_error', 'Failed to create file', array('status' => 500));
        }
        
        $file_id = $wpdb->insert_id;
        
        // Get the created file
        $created_file = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $file_id), ARRAY_A);
        
        if ($created_file['family_members']) {
            $created_file['family_members'] = json_decode($created_file['family_members'], true);
        }
        
        return new WP_REST_Response(array(
            'message' => 'File created successfully',
            'file' => $created_file
        ), 201);
    }
    
    /**
     * Update file
     */
    public function update_file($request) {
        global $wpdb;
        
        $id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'ivac_files';
        
        // Check if file exists
        $existing_file = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id), ARRAY_A);
        if (!$existing_file) {
            return new WP_Error('file_not_found', 'File not found', array('status' => 404));
        }
        
        // Prepare update data
        $data = array('updated_at' => current_time('mysql'));
        
        $updatable_fields = array('name', 'web_id', 'web_name', 'email', 'phone', 'password', 'location', 'ivac_center', 'visa_type', 'visa_category', 'status', 'payment_link', 'payment_method');
        
        foreach ($updatable_fields as $field) {
            $value = $request->get_param($field);
            if ($value !== null) {
                if ($field === 'email') {
                    $data[$field] = sanitize_email($value);
                } else {
                    $data[$field] = sanitize_text_field($value);
                }
            }
        }
        
        // Handle family members
        $family_members = $request->get_param('family_members');
        if ($family_members !== null) {
            $data['family_members'] = is_array($family_members) ? json_encode($family_members) : $family_members;
        }
        
        // Update processed_at if status is being changed to completed
        if (isset($data['status']) && $data['status'] === 'completed') {
            $data['processed_at'] = current_time('mysql');
        }
        
        // Update database
        $result = $wpdb->update($table_name, $data, array('id' => $id));
        
        if ($result === false) {
            return new WP_Error('db_error', 'Failed to update file', array('status' => 500));
        }
        
        // Get updated file
        $updated_file = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id), ARRAY_A);
        
        if ($updated_file['family_members']) {
            $updated_file['family_members'] = json_decode($updated_file['family_members'], true);
        }
        
        return new WP_REST_Response(array(
            'message' => 'File updated successfully',
            'file' => $updated_file
        ), 200);
    }
    
    /**
     * Delete file
     */
    public function delete_file($request) {
        global $wpdb;
        
        $id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'ivac_files';
        
        // Check if file exists
        $existing_file = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id), ARRAY_A);
        if (!$existing_file) {
            return new WP_Error('file_not_found', 'File not found', array('status' => 404));
        }
        
        // Delete from database
        $result = $wpdb->delete($table_name, array('id' => $id));
        
        if ($result === false) {
            return new WP_Error('db_error', 'Failed to delete file', array('status' => 500));
        }
        
        return new WP_REST_Response(array(
            'message' => 'File deleted successfully'
        ), 200);
    }
    
    /**
     * Get file schema for validation
     */
    private function get_file_schema() {
        return array(
            'name' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'web_id' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'email' => array(
                'required' => true,
                'type' => 'string',
                'format' => 'email',
                'sanitize_callback' => 'sanitize_email',
            ),
            'phone' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'password' => array(
                'required' => true,
                'type' => 'string',
            ),
            'location' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'ivac_center' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'visa_type' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'visa_category' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
        );
    }
}
