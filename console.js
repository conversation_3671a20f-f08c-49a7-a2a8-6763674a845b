(function() {
    // যদি পপ-আপ আগে থেকেই খোলা থাকে, তাহলে নতুন করে তৈরি করবে না
    if (document.getElementById('akash-login-popup-container')) {
        console.log('Login popup is already open.');
        return;
    }

    // --- ধাপ ১: CSS স্টাইল তৈরি করা (পপ-আপ এবং ওভারলে-এর জন্য) ---
    const cssStyles = `
        /* ব্যাকগ্রাউন্ড ওভারলে */
        #akash-login-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 9998;
            backdrop-filter: blur(4px); /* ব্যাকগ্রাউন্ড ব্লার করার জন্য */
        }

        /* মূল পপ-আপ কন্টেইনার */
        #akash-login-popup-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ffffff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            width: 320px;
            z-index: 9999;
            border: 1px solid #e0e0e0;
            color: #333; /* টেক্সটের রঙ পরিবর্তন */
        }

        /* Close বাটন */
        #akash-close-button {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            font-weight: bold;
            color: #888;
            cursor: pointer;
            line-height: 1;
        }
        #akash-close-button:hover {
            color: #000;
        }
        
        /* আগের স্টাইলগুলো এখানে অ্যাডজাস্ট করা হয়েছে */
        #akash-login-popup-container h3 {
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            color: #2c3e50;
            padding-bottom: 15px;
            border-bottom: 1px solid #eeeeee;
        }
        #akash-login-popup-container #ivac-status-panel {
            padding: 10px; margin: 0 0 15px 0; background: #f4f6f9; border-radius: 6px;
            font-size: 12px; min-height: 20px; text-align: center; font-weight: 500;
            word-wrap: break-word; border: 1px solid #e0e0e0;
        }
        #akash-login-popup-container input[type="text"], 
        #akash-login-popup-container input[type="password"] {
            width: 100%; padding: 12px; margin-bottom: 12px; border: 1px solid #ccc;
            border-radius: 6px; font-size: 14px; box-sizing: border-box; color: #333;
        }
        #akash-login-popup-container input::placeholder { color: #888; }
        #akash-login-popup-container #login-button {
            width: 100%; padding: 12px; background-color: #2c3e50; color: white;
            border: none; border-radius: 6px; cursor: pointer; font-size: 14px;
            font-weight: 600; text-transform: uppercase; transition: all 0.2s ease;
        }
        #akash-login-popup-container #login-button:hover { background-color: #34495e; }
        #akash-login-popup-container #login-button:disabled { background-color: #95a5a6; cursor: not-allowed; }
    `;

    // --- ধাপ ২: HTML কাঠামো তৈরি করা ---
    // ওভারলে এলিমেন্ট
    const overlay = document.createElement('div');
    overlay.id = 'akash-login-overlay';

    // পপ-আপ এলিমেন্ট
    const popup = document.createElement('div');
    popup.id = 'akash-login-popup-container';
    popup.innerHTML = `
        <button id="akash-close-button">×</button>
        <h3>Al Imran Akash</h3>
        <div id="ivac-status-panel">Ready to Auto Login</div>
        <input type="text" id="ivac-userid-input" placeholder="User ID">
        <input type="password" id="ivac-password-input" placeholder="Password">
        <button id="login-button">Login</button>
    `;

    // --- ধাপ ৩: পেজে CSS, ওভারলে এবং পপ-আপ যুক্ত করা ---
    const styleElement = document.createElement('style');
    styleElement.textContent = cssStyles;
    document.head.appendChild(styleElement);

    document.body.appendChild(overlay);
    document.body.appendChild(popup);

    // --- ধাপ ৪: জাভাস্ক্রিপ্ট লজিক ---
    // নতুন এবং আগের এলিমেন্টগুলো সিলেক্ট করা
    const statusPanel = document.getElementById('ivac-status-panel');
    const userIdInput = document.getElementById('ivac-userid-input');
    const passwordInput = document.getElementById('ivac-password-input');
    const loginButton = document.getElementById('login-button');
    const closeButton = document.getElementById('akash-close-button');

    const config = {
        endpoints: {
            login: 'https://payment.ivacbd.com/dologin',
            dashboard: 'https://payment.ivacbd.com/'
        },
        storageKey: 'ivac_credentials_akash'
    };
    
    // পপ-আপ বন্ধ করার ফাংশন
    function closePopup() {
        overlay.remove();
        popup.remove();
    }

    // আগের ফাংশনগুলো এখানে অপরিবর্তিত থাকবে
    function updateStatus(message, isError = false) {
        statusPanel.textContent = message;
        statusPanel.style.color = isError ? '#D32F2F' : '#388E3C';
    }
    function loadCredentials() {
        try {
            const savedCreds = localStorage.getItem(config.storageKey);
            if (savedCreds) {
                const { userId, password } = JSON.parse(savedCreds);
                if (userId) userIdInput.value = userId;
                if (password) passwordInput.value = password;
                updateStatus('Saved credentials loaded.');
            }
        } catch (e) { updateStatus('Failed to load credentials.', true); }
    }
    function saveCredentials(userId, password) {
        try {
            localStorage.setItem(config.storageKey, JSON.stringify({ userId, password }));
        } catch (e) { updateStatus('Failed to save credentials.', true); }
    }
    function getCsrfToken() {
        if (typeof window.csrf_token !== 'undefined') return window.csrf_token;
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) return metaTag.content;
        const inputField = document.querySelector('input[name="_token"]');
        if (inputField) return inputField.value;
        const scripts = document.querySelectorAll('script');
        for (let script of scripts) {
            const match = script.innerHTML.match(/var csrf_token = "([^"]+)"/);
            if (match && match[1]) return match[1];
        }
        return null;
    }

    // লগইন ফাংশন
    async function handleLogin() {
        const userId = userIdInput.value.trim();
        const password = passwordInput.value.trim();
        if (!userId || !password) {
            updateStatus('Please enter User ID and Password.', true);
            return;
        }
        loginButton.disabled = true;
        updateStatus('Attempting to log in...');
        saveCredentials(userId, password);
        
        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            updateStatus('CSRF token not found. Please run this on the target website.', true);
            loginButton.disabled = false;
            return;
        }
        updateStatus('CSRF Token Found. Logging in...');

        const formData = new URLSearchParams();
        formData.append('user_id', userId);
        formData.append('password', password);
        formData.append('_token', csrfToken);

        try {
            const response = await fetch(config.endpoints.login, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded', 'X-Requested-With': 'XMLHttpRequest' },
                body: formData,
                redirect: 'manual'
            });

            if (response.status === 302 || response.status === 200) {
                updateStatus('Login successful! Redirecting to dashboard...');
                closePopup(); // সফল হলে পপ-আপ বন্ধ করা
                setTimeout(() => {
                    window.location.href = config.endpoints.dashboard;
                }, 500);
            } else {
                updateStatus(`Login failed. Status: ${response.status}`, true);
                loginButton.disabled = false;
            }
        } catch (error) {
            updateStatus(`Network error: ${error.message}`, true);
            loginButton.disabled = false;
        }
    }

    // ইভেন্ট লিসেনার যুক্ত করা
    loginButton.addEventListener('click', handleLogin);
    closeButton.addEventListener('click', closePopup); // Close বাটনের জন্য ইভেন্ট
    overlay.addEventListener('click', closePopup); // ওভারলেতে ক্লিক করলেও পপ-আপ বন্ধ হবে
    
    loadCredentials();
})();