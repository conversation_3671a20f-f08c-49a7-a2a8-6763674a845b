document.addEventListener('DOMContentLoaded', function() {
    const rootElement = document.getElementById('ivac-react-dashboard-root');
    
    if (rootElement) {
        rootElement.innerHTML = `
            <div style="max-width: 800px; margin: 40px auto; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 30px; text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
                    <h2 style="color: #495057; margin-bottom: 16px;">React Dashboard Not Built</h2>
                    <p style="color: #6c757d; margin-bottom: 24px; line-height: 1.5;">
                        The React dashboard needs to be built before it can be displayed. 
                        Please follow the instructions below to set up the development environment.
                    </p>
                    
                    <div style="background: white; border: 1px solid #dee2e6; border-radius: 6px; padding: 20px; margin: 20px 0; text-align: left;">
                        <h3 style="color: #495057; margin-top: 0;">Setup Instructions:</h3>
                        <ol style="color: #6c757d; line-height: 1.6; padding-left: 20px;">
                            <li>Navigate to the plugin directory: <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">cd wp-content/plugins/ivac-dashboard/dashboard</code></li>
                            <li>Install dependencies: <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">npm install</code></li>
                            <li>Build for production: <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">npm run build</code></li>
                            <li>Or run in development mode: <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">npm run dev</code></li>
                        </ol>
                    </div>
                    
                    <div style="background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 6px; padding: 16px; margin: 20px 0;">
                        <p style="color: #0066cc; margin: 0; font-size: 14px;">
                            <strong>Note:</strong> Make sure you have Node.js (v16 or higher) and npm installed on your system.
                        </p>
                    </div>
                    
                    <button onclick="window.location.reload()" style="
                        background: #007cba; 
                        color: white; 
                        border: none; 
                        padding: 12px 24px; 
                        border-radius: 6px; 
                        cursor: pointer; 
                        font-size: 16px;
                        margin-top: 20px;
                    ">
                        Refresh Page
                    </button>
                </div>
            </div>
        `;
    }
});
