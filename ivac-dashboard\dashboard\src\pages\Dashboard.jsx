import React, { useEffect, useState } from 'react'
import { 
  Search, 
  Filter, 
  Download, 
  Trash2, 
  Edit, 
  Eye,
  RefreshCw,
  Plus,
  CheckSquare,
  Square,
  MoreHorizontal
} from 'lucide-react'
import { Link } from 'react-router-dom'
import toast from 'react-hot-toast'
import useStore from '../store/useStore'

const Dashboard = () => {
  const {
    files,
    filesLoading,
    filesError,
    pagination,
    filters,
    selectedFiles,
    fetchFiles,
    deleteFile,
    updateFile,
    setSelectedFiles,
    bulkDelete,
    bulkUpdateStatus,
    searchFiles,
    filterByStatus,
    resetFilters,
    goToPage
  } = useStore()

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Load files on component mount
  useEffect(() => {
    fetchFiles()
  }, [fetchFiles])

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault()
    searchFiles(searchTerm)
  }

  // Handle status filter
  const handleStatusFilter = (status) => {
    setStatusFilter(status)
    filterByStatus(status)
  }

  // Handle file selection
  const handleSelectFile = (fileId) => {
    const isSelected = selectedFiles.includes(fileId)
    if (isSelected) {
      setSelectedFiles(selectedFiles.filter(id => id !== fileId))
    } else {
      setSelectedFiles([...selectedFiles, fileId])
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      setSelectedFiles([])
    } else {
      setSelectedFiles(files.map(file => file.id))
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (!selectedFiles.length) return
    
    if (confirm(`Are you sure you want to delete ${selectedFiles.length} files?`)) {
      try {
        await bulkDelete(selectedFiles)
        toast.success(`${selectedFiles.length} files deleted successfully`)
        setShowBulkActions(false)
      } catch (error) {
        toast.error(error.message)
      }
    }
  }

  // Handle bulk status update
  const handleBulkStatusUpdate = async (status) => {
    if (!selectedFiles.length) return
    
    try {
      await bulkUpdateStatus(selectedFiles, status)
      toast.success(`${selectedFiles.length} files updated successfully`)
      setSelectedFiles([])
      setShowBulkActions(false)
    } catch (error) {
      toast.error(error.message)
    }
  }

  // Handle single file delete
  const handleDeleteFile = async (fileId) => {
    if (confirm('Are you sure you want to delete this file?')) {
      try {
        await deleteFile(fileId)
        toast.success('File deleted successfully')
      } catch (error) {
        toast.error(error.message)
      }
    }
  }

  // Handle status update
  const handleStatusUpdate = async (fileId, status) => {
    try {
      await updateFile(fileId, { status })
      toast.success('Status updated successfully')
    } catch (error) {
      toast.error(error.message)
    }
  }

  // Get status badge class
  const getStatusBadge = (status) => {
    const badges = {
      pending: 'badge-gray',
      processing: 'badge-info',
      completed: 'badge-success',
      error: 'badge-error',
      login_failed: 'badge-error',
      otp_required: 'badge-warning',
      captcha_failed: 'badge-warning',
      skipped: 'badge-gray'
    }
    return badges[status] || 'badge-gray'
  }

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (filesError) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading files: {filesError}</div>
        <button 
          onClick={() => fetchFiles()} 
          className="btn-primary"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Visa Files</h2>
          <p className="text-gray-600">Manage and track your IVAC visa applications</p>
        </div>
        <Link to="/add-file" className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Add New File
        </Link>
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name, email, or web ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>
          </form>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => handleStatusFilter(e.target.value)}
            className="select w-48"
          >
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="error">Error</option>
            <option value="login_failed">Login Failed</option>
            <option value="otp_required">OTP Required</option>
            <option value="captcha_failed">Captcha Failed</option>
          </select>

          {/* Actions */}
          <div className="flex gap-2">
            <button
              onClick={() => fetchFiles()}
              disabled={filesLoading}
              className="btn-outline"
            >
              <RefreshCw className={`h-4 w-4 ${filesLoading ? 'animate-spin' : ''}`} />
            </button>
            
            <button
              onClick={resetFilters}
              className="btn-outline"
            >
              <Filter className="h-4 w-4" />
              Reset
            </button>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedFiles.length > 0 && (
        <div className="card bg-primary-50 border-primary-200">
          <div className="flex items-center justify-between">
            <span className="text-primary-700 font-medium">
              {selectedFiles.length} files selected
            </span>
            <div className="flex gap-2">
              <button
                onClick={() => handleBulkStatusUpdate('pending')}
                className="btn-sm btn-outline"
              >
                Mark Pending
              </button>
              <button
                onClick={() => handleBulkStatusUpdate('completed')}
                className="btn-sm btn-outline"
              >
                Mark Completed
              </button>
              <button
                onClick={handleBulkDelete}
                className="btn-sm btn-danger"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Files Table */}
      <div className="card p-0">
        {filesLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="loading-spinner h-8 w-8"></div>
            <span className="ml-2 text-gray-600">Loading files...</span>
          </div>
        ) : files.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">No files found</div>
            <Link to="/add-file" className="btn-primary">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First File
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th className="w-12">
                    <button
                      onClick={handleSelectAll}
                      className="p-1 hover:bg-gray-100 rounded"
                    >
                      {selectedFiles.length === files.length ? (
                        <CheckSquare className="h-4 w-4 text-primary-600" />
                      ) : (
                        <Square className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </th>
                  <th>Index</th>
                  <th>Info</th>
                  <th>Web ID</th>
                  <th>Web Name</th>
                  <th>Password</th>
                  <th>Visa Info</th>
                  <th>Payment Method</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Operation</th>
                </tr>
              </thead>
              <tbody>
                {files.map((file, index) => (
                  <tr key={file.id}>
                    <td>
                      <button
                        onClick={() => handleSelectFile(file.id)}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        {selectedFiles.includes(file.id) ? (
                          <CheckSquare className="h-4 w-4 text-primary-600" />
                        ) : (
                          <Square className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </td>
                    <td className="font-medium">
                      {(pagination.current_page - 1) * pagination.per_page + index + 1}
                    </td>
                    <td>
                      <div>
                        <div className="font-medium text-gray-900">{file.name}</div>
                        <div className="text-sm text-gray-500">{file.email}</div>
                        <div className="text-sm text-gray-500">{file.phone}</div>
                      </div>
                    </td>
                    <td className="font-mono text-sm">{file.web_id}</td>
                    <td>{file.web_name || '-'}</td>
                    <td>
                      <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                        {'*'.repeat(file.password?.length || 0)}
                      </span>
                    </td>
                    <td>
                      <div className="text-sm">
                        <div>{file.visa_type}</div>
                        <div className="text-gray-500">{file.visa_category}</div>
                        <div className="text-gray-500">{file.location} - {file.ivac_center}</div>
                      </div>
                    </td>
                    <td>{file.payment_method || '-'}</td>
                    <td>
                      <span className={`badge ${getStatusBadge(file.status)}`}>
                        {file.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="text-sm text-gray-500">
                      {formatDate(file.created_at)}
                    </td>
                    <td>
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => handleStatusUpdate(file.id, file.status === 'pending' ? 'processing' : 'pending')}
                          className="p-1 hover:bg-gray-100 rounded"
                          title="Toggle Status"
                        >
                          <Edit className="h-4 w-4 text-gray-400" />
                        </button>
                        <button
                          onClick={() => handleDeleteFile(file.id)}
                          className="p-1 hover:bg-red-100 rounded"
                          title="Delete"
                        >
                          <Trash2 className="h-4 w-4 text-red-400" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {(pagination.current_page - 1) * pagination.per_page + 1} to{' '}
            {Math.min(pagination.current_page * pagination.per_page, pagination.total)} of{' '}
            {pagination.total} results
          </div>
          
          <div className="flex gap-1">
            <button
              onClick={() => goToPage(pagination.current_page - 1)}
              disabled={pagination.current_page === 1}
              className="btn-outline btn-sm disabled:opacity-50"
            >
              Previous
            </button>
            
            {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
              const page = i + 1
              return (
                <button
                  key={page}
                  onClick={() => goToPage(page)}
                  className={`btn-sm ${
                    page === pagination.current_page ? 'btn-primary' : 'btn-outline'
                  }`}
                >
                  {page}
                </button>
              )
            })}
            
            <button
              onClick={() => goToPage(pagination.current_page + 1)}
              disabled={pagination.current_page === pagination.pages}
              className="btn-outline btn-sm disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default Dashboard
